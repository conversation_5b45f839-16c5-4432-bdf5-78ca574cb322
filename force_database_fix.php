<?php
// Force Database Fix - Override all configurations
echo "<h1>🔧 Force Database Fix</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px;'>";

try {
    echo "<h2>🔍 Step 1: Direct Database Connection Test</h2>";
    
    // Force connection to db_tewuneed2 directly
    $pdo = new PDO("mysql:host=localhost;dbname=db_tewuneed2;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Direct connection to db_tewuneed2: SUCCESS</p>";
    
    echo "<h2>📋 Step 2: Check Table Structure</h2>";
    
    // Check tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>Tables in db_tewuneed2:</strong> " . implode(', ', $tables) . "</p>";
    
    // Check products table structure
    if (in_array('products', $tables)) {
        $stmt = $pdo->query("DESCRIBE products");
        $columns = $stmt->fetchAll();
        echo "<p><strong>Products table columns:</strong></p>";
        echo "<ul>";
        foreach ($columns as $col) {
            echo "<li>" . $col['Field'] . " (" . $col['Type'] . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>📊 Step 3: Check Data</h2>";
    
    // Count products
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
    $total_products = $stmt->fetch()['total'];
    echo "<p><strong>Total products:</strong> $total_products</p>";
    
    // Count active products
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    $active_products = $stmt->fetch()['total'];
    echo "<p><strong>Active products:</strong> $active_products</p>";
    
    // Test the exact query from products.php
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name, c.slug as category_slug
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 5
    ");
    $test_products = $stmt->fetchAll();
    echo "<p><strong>Query test result:</strong> " . count($test_products) . " products found</p>";
    
    if (!empty($test_products)) {
        echo "<h3>📦 Sample Products Found:</h3>";
        echo "<ul>";
        foreach ($test_products as $product) {
            echo "<li><strong>" . htmlspecialchars($product['name']) . "</strong> - Rp " . number_format($product['price'], 0, ',', '.') . "</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>🔧 Step 4: Force Update Database Configuration</h2>";
    
    // Create a new database config file that forces db_tewuneed2
    $new_config = '<?php
// Database Configuration - FORCED TO db_tewuneed2
define("DB_HOST", "localhost");
define("DB_NAME", "db_tewuneed2");
define("DB_USER", "root");
define("DB_PASS", "");

// Create database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Function to get database connection
function getDBConnection() {
    global $pdo;
    return $pdo;
}
?>';
    
    file_put_contents('config/database.php', $new_config);
    echo "<p style='color: green;'>✅ Updated config/database.php to force db_tewuneed2</p>";
    
    echo "<h2>🧪 Step 5: Test Website Configuration</h2>";
    
    // Test the config file
    unset($pdo); // Clear existing connection
    require_once 'config/config.php';
    
    $test_pdo = getDBConnection();
    $stmt = $test_pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    $config_test_count = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Website config test: $config_test_count active products found</p>";
    
    if ($config_test_count > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin: 0 0 15px 0;'>🎉 SUCCESS!</h3>";
        echo "<p style='color: #155724; margin: 0;'>Database configuration fixed! Your products page should now work.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #856404; margin: 0 0 15px 0;'>⚠️ Configuration Updated</h3>";
        echo "<p style='color: #856404; margin: 0;'>Database config updated but no products found. May need to import data.</p>";
        echo "</div>";
    }
    
    echo "<h2>🚀 Test Your Website Now</h2>";
    echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
    echo "<a href='products.php' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🛍️ Test Products Page</a>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🏠 Home Page</a>";
    echo "<a href='cart.php' style='background: #ffc107; color: black; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🛒 Shopping Cart</a>";
    echo "</div>";
    
    echo "<h2>📋 Debug Information</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p><strong>Current database:</strong> " . DB_NAME . "</p>";
    echo "<p><strong>Products in database:</strong> $config_test_count</p>";
    echo "<p><strong>Config file location:</strong> " . realpath('config/database.php') . "</p>";
    echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime('config/database.php')) . "</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 15px 0;'>❌ Error</h3>";
    echo "<p style='color: #721c24; margin: 0;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<h3>🔧 Emergency Steps:</h3>";
    echo "<ol>";
    echo "<li>Make sure XAMPP is running</li>";
    echo "<li>Start MySQL service</li>";
    echo "<li>Open phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "<li>Check if database 'db_tewuneed2' exists</li>";
    echo "<li>If not, create it manually and run the setup again</li>";
    echo "</ol>";
}

echo "</div>";
?>

<style>
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1, h2, h3 {
    color: #333;
}
</style>
