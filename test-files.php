<?php
echo "<h1>🔍 File Existence Test</h1>";

$files_to_check = [
    'login.php',
    'register.php', 
    'my-orders.php',
    'logout.php',
    'index.php',
    'products.php',
    'api/firebase-session.php',
    'config/config.php'
];

echo "<h2>File Status Check</h2>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>File</th><th>Status</th><th>Size</th><th>Action</th></tr>";

foreach ($files_to_check as $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    $status_color = $exists ? 'green' : 'red';
    $status_text = $exists ? '✓ Exists' : '✗ Missing';
    
    echo "<tr>";
    echo "<td><code>$file</code></td>";
    echo "<td style='color: $status_color;'>$status_text</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : 'N/A') . "</td>";
    echo "<td>";
    if ($exists && in_array($file, ['login.php', 'register.php', 'my-orders.php', 'index.php'])) {
        echo "<a href='$file' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test</a>";
    }
    echo "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>Quick Navigation</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔑 Login Page</a>";
echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📝 Register Page</a>";
echo "<a href='my-orders.php' style='background: #fd7e14; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📦 My Orders</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Homepage</a>";
echo "<a href='setup-firebase-users.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔥 Setup Firebase</a>";
echo "</div>";

echo "<h2>Instructions</h2>";
echo "<ol>";
echo "<li><strong>Setup Firebase Users:</strong> Go to <a href='setup-firebase-users.php'>setup-firebase-users.php</a> and create demo accounts</li>";
echo "<li><strong>Test Login:</strong> Go to <a href='login.php'>login.php</a> and login with demo credentials</li>";
echo "<li><strong>Test My Orders:</strong> After login, go to <a href='my-orders.php'>my-orders.php</a> to see orders</li>";
echo "</ol>";

echo "<h2>Demo Credentials</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Test User:</strong> <EMAIL> / user123</p>";
echo "<p><strong>John Doe:</strong> <EMAIL> / john123</p>";
echo "<p><strong>Admin:</strong> <EMAIL> / admin123</p>";
echo "</div>";
?>
