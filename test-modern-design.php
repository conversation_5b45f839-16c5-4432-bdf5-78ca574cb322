<?php
require_once 'config/config.php';

$page_title = 'Modern Design Test';
$page_description = 'Testing the new modern design system for TeWuNeed';

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">🎨 Modern Design System Test</h1>
        <p class="lead text-gray-600">Testing the new professional design components</p>
    </div>

    <!-- Design System Overview -->
    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="card-modern text-center">
                <div class="card-body">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-palette fa-lg text-white"></i>
                    </div>
                    <h5 class="fw-bold">Modern Colors</h5>
                    <p class="text-gray-600 small">Professional color palette with CSS variables</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card-modern text-center">
                <div class="card-body">
                    <div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-font fa-lg text-white"></i>
                    </div>
                    <h5 class="fw-bold">Typography</h5>
                    <p class="text-gray-600 small">Inter & Playfair Display fonts with proper hierarchy</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card-modern text-center">
                <div class="card-body">
                    <div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-magic fa-lg text-white"></i>
                    </div>
                    <h5 class="fw-bold">Components</h5>
                    <p class="text-gray-600 small">Modern cards, buttons, and interactive elements</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Button Showcase -->
    <div class="card-modern mb-5">
        <div class="card-header">
            <h4 class="mb-0">Modern Button Styles</h4>
        </div>
        <div class="card-body">
            <div class="d-flex flex-wrap gap-3 mb-4">
                <button class="btn btn-modern-primary">Primary Button</button>
                <button class="btn btn-modern-secondary">Secondary Button</button>
                <button class="btn btn-outline-primary">Outline Primary</button>
                <button class="btn btn-outline-secondary">Outline Secondary</button>
            </div>
            <div class="d-flex flex-wrap gap-3">
                <button class="btn btn-modern-primary btn-lg">Large Button</button>
                <button class="btn btn-modern-secondary btn-sm">Small Button</button>
                <button class="btn btn-modern-primary" disabled>Disabled Button</button>
            </div>
        </div>
    </div>

    <!-- Card Showcase -->
    <div class="row g-4 mb-5">
        <div class="col-md-6">
            <div class="card-modern hover-lift">
                <div class="card-header">
                    <h5 class="mb-0">Modern Card with Hover</h5>
                </div>
                <div class="card-body">
                    <p>This card has a modern design with hover effects. It uses the new shadow system and border radius.</p>
                    <div class="d-flex gap-2">
                        <span class="badge badge-primary">Modern</span>
                        <span class="badge badge-success">Responsive</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="product-card-modern">
                <div class="product-image">
                    <img src="<?php echo SITE_URL; ?>/uploads/default-product.jpg" alt="Sample Product">
                    <div class="product-overlay">
                        <div class="product-actions">
                            <button class="btn btn-modern-primary btn-sm flex-fill">Add to Cart</button>
                            <button class="btn btn-outline-light btn-sm">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="product-info">
                    <h6 class="product-title">Sample Product</h6>
                    <div class="product-price">$99.99</div>
                    <div class="product-rating">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                        </div>
                        <span class="rating-text">(4.0)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Showcase -->
    <div class="card-modern mb-5">
        <div class="card-header">
            <h4 class="mb-0">Modern Form Elements</h4>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label fw-semibold">Modern Input</label>
                    <input type="text" class="form-control-modern" placeholder="Enter your text here...">
                </div>
                <div class="col-md-6">
                    <label class="form-label fw-semibold">Select Dropdown</label>
                    <select class="form-control-modern">
                        <option>Choose an option</option>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
                <div class="col-12">
                    <label class="form-label fw-semibold">Textarea</label>
                    <textarea class="form-control-modern" rows="3" placeholder="Enter your message..."></textarea>
                </div>
            </div>
        </div>
    </div>

    <!-- Color Palette -->
    <div class="card-modern mb-5">
        <div class="card-header">
            <h4 class="mb-0">Color Palette</h4>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <h6>Primary Colors</h6>
                    <div class="d-flex gap-2 mb-3">
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--primary-500);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--primary-600);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--primary-700);"></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>Secondary Colors</h6>
                    <div class="d-flex gap-2 mb-3">
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--secondary-400);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--secondary-500);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--secondary-600);"></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>Status Colors</h6>
                    <div class="d-flex gap-2 mb-3">
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--success);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--warning);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--error);"></div>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>Accent Colors</h6>
                    <div class="d-flex gap-2 mb-3">
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--accent-orange);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--accent-green);"></div>
                        <div class="rounded" style="width: 40px; height: 40px; background-color: var(--accent-purple);"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Test -->
    <div class="text-center">
        <h3 class="mb-4">Test Navigation</h3>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/index.php" class="btn btn-modern-primary">
                <i class="fas fa-home me-2"></i>Homepage
            </a>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-modern-secondary">
                <i class="fas fa-shopping-cart me-2"></i>Products
            </a>
            <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-outline-primary">
                <i class="fas fa-sign-in-alt me-2"></i>Login
            </a>
            <a href="<?php echo SITE_URL; ?>/my-orders.php" class="btn btn-outline-secondary">
                <i class="fas fa-shopping-bag me-2"></i>My Orders
            </a>
        </div>
    </div>
</div>

<style>
/* Additional test styles */
.min-vh-75 {
    min-height: 75vh;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.floating-product {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}
</style>

<?php include 'includes/footer.php'; ?>
