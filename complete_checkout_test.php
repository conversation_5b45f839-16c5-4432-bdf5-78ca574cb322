<?php
// Complete Checkout Process Test
require_once 'config/config.php';

echo "<h1>🧪 Complete Checkout Process Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px;'>";

try {
    $pdo = getDBConnection();
    
    echo "<h2>Step 1: Setup Test Environment</h2>";
    
    // Clear any existing test data
    $session_id = session_id();
    $pdo->exec("DELETE FROM cart WHERE session_id = '$session_id' AND user_id IS NULL");
    
    // Get test products
    $stmt = $pdo->query("SELECT id, name, price, sale_price FROM products WHERE status = 'active' LIMIT 3");
    $test_products = $stmt->fetchAll();
    
    if (empty($test_products)) {
        echo "<p style='color: red;'>❌ No products available for testing</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ Test environment ready</p>";
    
    echo "<h2>Step 2: Add Products to Cart</h2>";
    
    $cart_total = 0;
    foreach ($test_products as $product) {
        $price = $product['sale_price'] ?: $product['price'];
        $quantity = rand(1, 3);
        
        $stmt = $pdo->prepare("
            INSERT INTO cart (session_id, product_id, quantity, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$session_id, $product['id'], $quantity]);
        
        $item_total = $price * $quantity;
        $cart_total += $item_total;
        
        echo "<p>• Added {$product['name']} (Qty: $quantity) - " . formatPrice($item_total) . "</p>";
    }
    
    echo "<p><strong>Cart Total: " . formatPrice($cart_total) . "</strong></p>";
    echo "<p style='color: green;'>✓ Products added to cart</p>";
    
    echo "<h2>Step 3: Simulate Checkout Form Submission</h2>";
    
    // Simulate checkout data
    $checkout_data = [
        'first_name' => 'Test',
        'last_name' => 'Customer',
        'email' => '<EMAIL>',
        'phone' => '************',
        'address_line_1' => 'Jl. Sudirman No. 123',
        'address_line_2' => 'Apt 4B',
        'city' => 'Jakarta',
        'state' => 'DKI Jakarta',
        'postal_code' => '12345',
        'country' => 'Indonesia',
        'payment_method' => 'Bank Transfer BCA',
        'notes' => 'Test order - please handle with care',
        'coupon_code' => '',
        'discount_amount' => 0,
        'coupon_id' => null
    ];
    
    $_SESSION['checkout_data'] = $checkout_data;
    echo "<p style='color: green;'>✓ Checkout data prepared</p>";
    
    echo "<h2>Step 4: Process Order</h2>";
    
    // Calculate order totals
    $subtotal = $cart_total;
    $shipping_fee = $subtotal >= 500000 ? 0 : 25000;
    $tax_rate = 0.11;
    $tax_amount = $subtotal * $tax_rate;
    $discount_amount = 0;
    $total_amount = $subtotal + $shipping_fee + $tax_amount - $discount_amount;
    
    echo "<div class='order-summary' style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h6>Order Summary:</h6>";
    echo "<p>Subtotal: " . formatPrice($subtotal) . "</p>";
    echo "<p>Shipping: " . ($shipping_fee > 0 ? formatPrice($shipping_fee) : 'FREE') . "</p>";
    echo "<p>Tax (11%): " . formatPrice($tax_amount) . "</p>";
    echo "<p><strong>Total: " . formatPrice($total_amount) . "</strong></p>";
    echo "</div>";
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Generate order number
    $order_number = 'TWN-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // Create shipping address
    $shipping_address = "{$checkout_data['first_name']} {$checkout_data['last_name']}\n{$checkout_data['address_line_1']}";
    if ($checkout_data['address_line_2']) $shipping_address .= "\n{$checkout_data['address_line_2']}";
    $shipping_address .= "\n{$checkout_data['city']}, {$checkout_data['state']} {$checkout_data['postal_code']}\n{$checkout_data['country']}";
    if ($checkout_data['phone']) $shipping_address .= "\nPhone: {$checkout_data['phone']}";
    
    // Customer info for guest checkout
    $customer_info = json_encode([
        'first_name' => $checkout_data['first_name'],
        'last_name' => $checkout_data['last_name'],
        'email' => $checkout_data['email'],
        'phone' => $checkout_data['phone']
    ]);
    
    // Insert order
    $stmt = $pdo->prepare("
        INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount,
                          tax_amount, discount_amount, payment_method, payment_status,
                          shipping_address, notes, coupon_id, customer_info)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
    $stmt->execute([
        $order_number, $user_id, 'pending', $total_amount, $shipping_fee, $tax_amount,
        $discount_amount, $checkout_data['payment_method'], 'pending',
        $shipping_address, $checkout_data['notes'], $checkout_data['coupon_id'], $customer_info
    ]);
    
    $order_id = $pdo->lastInsertId();
    
    echo "<p style='color: green;'>✓ Order created: $order_number (ID: $order_id)</p>";
    
    echo "<h2>Step 5: Add Order Items</h2>";
    
    // Get cart items for order
    $stmt = $pdo->prepare("
        SELECT c.*, p.name, p.price, p.sale_price 
        FROM cart c 
        JOIN products p ON c.product_id = p.id 
        WHERE c.session_id = ? AND c.user_id IS NULL
    ");
    $stmt->execute([$session_id]);
    $cart_items = $stmt->fetchAll();
    
    foreach ($cart_items as $item) {
        $price = $item['sale_price'] ?: $item['price'];
        $total = $price * $item['quantity'];
        
        $stmt = $pdo->prepare("
            INSERT INTO order_items (order_id, product_id, quantity, price, total) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $order_id, 
            $item['product_id'], 
            $item['quantity'], 
            $price, 
            $total
        ]);
        
        // Update product stock
        $stmt = $pdo->prepare("UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?");
        $stmt->execute([$item['quantity'], $item['product_id']]);
        
        echo "<p>• Added order item: {$item['name']} (Qty: {$item['quantity']}) - " . formatPrice($total) . "</p>";
    }
    
    echo "<p style='color: green;'>✓ Order items added and stock updated</p>";
    
    echo "<h2>Step 6: Clear Cart and Finalize</h2>";
    
    // Clear cart
    $stmt = $pdo->prepare("DELETE FROM cart WHERE session_id = ? AND user_id IS NULL");
    $stmt->execute([$session_id]);
    
    // Commit transaction
    $pdo->commit();
    
    // Clear checkout session data
    unset($_SESSION['checkout_data']);
    
    echo "<p style='color: green;'>✓ Cart cleared and order finalized</p>";
    
    echo "<h2>✅ Order Successfully Created!</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4 style='color: #155724; margin-top: 0;'>Order Details:</h4>";
    echo "<p><strong>Order Number:</strong> $order_number</p>";
    echo "<p><strong>Total Amount:</strong> " . formatPrice($total_amount) . "</p>";
    echo "<p><strong>Payment Method:</strong> {$checkout_data['payment_method']}</p>";
    echo "<p><strong>Status:</strong> Pending</p>";
    echo "<p><strong>Payment Status:</strong> Pending Payment</p>";
    echo "</div>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='" . SITE_URL . "/order-confirmation.php?order=$order_number' class='btn btn-primary' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>";
    echo "<i class='fas fa-eye'></i> View Order Confirmation";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}

echo "</div>";
?>
