-- Additional tables for Admin Dashboard
-- Run this SQL script in SQLyog or phpMyAdmin

USE db_tewuneed2;

-- Reviews table (simplified version for admin dashboard)
CREATE TABLE IF NOT EXISTS reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    user_id INT,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_product (product_id),
    INDEX idx_user (user_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status)
);

-- Coupons table (updated structure for admin dashboard)
CREATE TABLE IF NOT EXISTS coupons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    type ENUM('percentage', 'fixed') NOT NULL,
    value DECIMAL(10,2) NOT NULL,
    min_amount DECIMAL(10,2) DEFAULT 0,
    max_uses INT DEFAULT 0,
    used_count INT DEFAULT 0,
    expires_at TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_expires (expires_at)
);

-- Settings table for website configuration
CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key)
);

-- Insert sample reviews
INSERT INTO reviews (product_id, user_id, rating, comment, status) VALUES
(1, 1, 5, 'Amazing iPhone! The camera quality is outstanding and battery life is excellent. Highly recommended for anyone looking for a premium smartphone.', 'approved'),
(1, 2, 4, 'Great phone but quite expensive. The features are impressive and performance is smooth. Overall satisfied with the purchase.', 'approved'),
(1, 3, 5, 'Best iPhone I have ever used. The Dynamic Island feature is innovative and the build quality is top-notch.', 'pending'),
(2, 1, 4, 'Excellent Android phone with great camera system. The display is vibrant and the performance is very smooth.', 'approved'),
(2, 2, 5, 'Samsung Galaxy S23 exceeded my expectations. Fast processor, beautiful design, and amazing camera quality.', 'approved'),
(4, 1, 5, 'This vitamin C serum is incredible! My skin looks brighter and more radiant after just 2 weeks of use.', 'approved'),
(4, 3, 5, 'Best serum I have ever tried. Visible results in reducing dark spots and improving skin texture.', 'approved'),
(4, 2, 4, 'Good quality serum with noticeable results. A bit pricey but worth the investment for healthy skin.', 'pending'),
(7, 2, 4, 'Solid dumbbell set perfect for home workouts. Easy to adjust weights and good build quality.', 'approved'),
(7, 3, 5, 'Excellent dumbbells for strength training. The weight adjustment mechanism is smooth and secure.', 'approved'),
(11, 1, 5, 'Classic Oreo cookies that never disappoint. Perfect for snacking and sharing with family.', 'approved'),
(11, 2, 4, 'Delicious cookies with the perfect balance of chocolate and cream. Always a favorite treat.', 'approved'),
(15, 1, 5, 'Sony headphones are amazing! Noise cancellation is incredible and sound quality is top-notch.', 'approved'),
(15, 3, 5, 'Best headphones for music lovers. Comfortable to wear for long periods and excellent audio quality.', 'approved'),
(18, 2, 4, 'Great foundation with good coverage and natural finish. Lasts all day without looking cakey.', 'approved'),
(19, 1, 5, 'Complete skincare set that transformed my skin routine. All products work well together.', 'approved'),
(23, 2, 5, 'Premium coffee beans with rich flavor and aroma. Perfect for coffee enthusiasts who appreciate quality.', 'approved'),
(26, 3, 4, 'Effective multivitamin supplement. Feel more energetic since I started taking these daily.', 'approved'),
(13, 1, 5, 'Fresh and organic tomatoes with great taste. Perfect for cooking and making fresh salads.', 'approved'),
(14, 2, 4, 'Nutritious broccoli that stays fresh for days. Great addition to healthy meals and stir-fries.', 'approved');

-- Insert sample coupons
INSERT INTO coupons (code, description, type, value, min_amount, max_uses, expires_at, status) VALUES
('WELCOME10', 'Welcome discount for new customers - Get 10% off on your first order', 'percentage', 10.00, 100000, 100, DATE_ADD(NOW(), INTERVAL 30 DAY), 'active'),
('SAVE50K', 'Save Rp 50,000 on orders above Rp 500,000', 'fixed', 50000.00, 500000, 50, DATE_ADD(NOW(), INTERVAL 15 DAY), 'active'),
('FREESHIP', 'Free shipping coupon - Get free delivery on any order', 'fixed', 25000.00, 0, 200, DATE_ADD(NOW(), INTERVAL 7 DAY), 'active'),
('ELECTRONICS20', 'Special discount for electronics category - 20% off', 'percentage', 20.00, 200000, 75, DATE_ADD(NOW(), INTERVAL 10 DAY), 'active'),
('COSMETICS15', 'Beauty products discount - 15% off on all cosmetics', 'percentage', 15.00, 150000, 60, DATE_ADD(NOW(), INTERVAL 20 DAY), 'active'),
('SPORTS25K', 'Sports equipment discount - Rp 25,000 off', 'fixed', 25000.00, 300000, 40, DATE_ADD(NOW(), INTERVAL 12 DAY), 'active'),
('HEALTH100K', 'Health products mega discount - Rp 100,000 off', 'fixed', 100000.00, 800000, 25, DATE_ADD(NOW(), INTERVAL 5 DAY), 'active'),
('WEEKEND30', 'Weekend special - 30% off on selected items', 'percentage', 30.00, 250000, 30, DATE_ADD(NOW(), INTERVAL 3 DAY), 'active'),
('EXPIRED10', 'Expired test coupon', 'percentage', 10.00, 50000, 50, DATE_SUB(NOW(), INTERVAL 5 DAY), 'expired'),
('INACTIVE20', 'Inactive test coupon', 'percentage', 20.00, 100000, 100, DATE_ADD(NOW(), INTERVAL 30 DAY), 'inactive');

-- Insert website settings
INSERT INTO settings (setting_key, setting_value) VALUES
('site_name', 'TeWuNeed'),
('site_description', 'Your trusted online shopping destination'),
('site_email', '<EMAIL>'),
('site_phone', '+62 ************'),
('site_address', 'Jakarta, Indonesia'),
('currency', 'IDR'),
('tax_rate', '10'),
('shipping_fee', '15000'),
('free_shipping_min', '100000'),
('order_prefix', 'TWN'),
('items_per_page', '12'),
('maintenance_mode', '0'),
('allow_guest_checkout', '1'),
('email_notifications', '1'),
('auto_approve_reviews', '0')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Update some sample orders to have more realistic data
UPDATE orders SET 
    created_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
    updated_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 25) DAY)
WHERE id IN (1, 2, 3);

-- Add more sample orders for better dashboard statistics
INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount, tax_amount, payment_method, payment_status, shipping_address, created_at) VALUES
('TWN-2024-004', 1, 'pending', 350000, 15000, 35000, 'Bank Transfer BCA', 'pending', 'Test User\nJl. Sudirman No. 123\nJakarta Pusat, DKI Jakarta 10110', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('TWN-2024-005', 2, 'processing', 1250000, 0, 125000, 'GoPay', 'paid', 'John Doe\nJl. Thamrin No. 456\nJakarta Pusat, DKI Jakarta 10230', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('TWN-2024-006', 3, 'shipped', 580000, 25000, 58000, 'OVO', 'paid', 'Jane Smith\nJl. Gatot Subroto No. 789\nJakarta Selatan, DKI Jakarta 12930', DATE_SUB(NOW(), INTERVAL 3 DAY)),
('TWN-2024-007', 1, 'delivered', 4200000, 0, 420000, 'Credit Card', 'paid', 'Test User\nJl. Sudirman No. 123\nJakarta Pusat, DKI Jakarta 10110', DATE_SUB(NOW(), INTERVAL 5 DAY)),
('TWN-2024-008', 2, 'cancelled', 180000, 15000, 18000, 'Dana', 'refunded', 'John Doe\nJl. Thamrin No. 456\nJakarta Pusat, DKI Jakarta 10230', DATE_SUB(NOW(), INTERVAL 7 DAY));

-- Insert order items for the new orders
INSERT INTO order_items (order_id, product_id, quantity, price, total) VALUES
-- Order 4 items
(4, 4, 1, 200000, 200000),
(4, 11, 6, 22000, 132000),
-- Order 5 items  
(5, 15, 1, 4200000, 4200000),
(5, 18, 3, 280000, 840000),
-- Order 6 items
(6, 7, 1, 750000, 750000),
(6, 19, 1, 580000, 580000),
-- Order 7 items
(7, 15, 1, 4200000, 4200000),
-- Order 8 items
(8, 4, 1, 200000, 200000);

-- Update coupon usage count for some coupons
UPDATE coupons SET used_count = FLOOR(RAND() * 10) + 1 WHERE id IN (1, 2, 3, 4, 5);

-- Add some product attributes for better product details
INSERT INTO product_attributes (product_id, attribute_name, attribute_value) VALUES
-- More iPhone attributes
(1, 'Operating System', 'iOS 16'),
(1, 'Battery Life', '23 hours video playback'),
(1, 'Water Resistance', 'IP68'),
(1, 'Wireless Charging', 'Yes'),
-- Samsung Galaxy attributes
(2, 'Operating System', 'Android 13'),
(2, 'Battery Capacity', '3900mAh'),
(2, 'Fast Charging', '25W'),
(2, 'Water Resistance', 'IP68'),
-- Serum attributes
(4, 'Ingredients', 'Vitamin C, Hyaluronic Acid, Vitamin E'),
(4, 'Usage', 'Apply morning and evening'),
(4, 'Shelf Life', '12 months after opening'),
(4, 'Cruelty Free', 'Yes');

-- Create indexes for better performance
CREATE INDEX idx_orders_created ON orders(created_at);
CREATE INDEX idx_reviews_created ON reviews(created_at);
CREATE INDEX idx_products_featured ON products(featured);
CREATE INDEX idx_users_created ON users(created_at);

-- Update some product stock quantities to show low stock alerts
UPDATE products SET stock_quantity = 2 WHERE id IN (3, 8, 20);
UPDATE products SET stock_quantity = 1 WHERE id IN (16, 21);
UPDATE products SET stock_quantity = 0 WHERE id IN (17, 22);

COMMIT;
