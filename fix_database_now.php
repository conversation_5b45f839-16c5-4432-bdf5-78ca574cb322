<?php
// EMERGENCY DATABASE FIX - Immediate Solution
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 EMERGENCY DATABASE FIX</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px;'>";

try {
    // Database configuration
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'db_tewuneed2';
    
    echo "<h2>Step 1: Connect to MySQL</h2>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ MySQL connected</p>";
    
    // Drop and recreate database
    echo "<h2>Step 2: Fresh Database Creation</h2>";
    $pdo->exec("DROP DATABASE IF EXISTS `$database`");
    $pdo->exec("CREATE DATABASE `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$database`");
    echo "<p style='color: green;'>✓ Database recreated: $database</p>";
    
    // Create tables with exact structure needed
    echo "<h2>Step 3: Creating Tables</h2>";
    
    // Categories table
    $pdo->exec("
        CREATE TABLE `categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `slug` varchar(100) NOT NULL,
            `description` text DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `status` enum('active','inactive') DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `slug` (`slug`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Categories table created</p>";
    
    // Products table
    $pdo->exec("
        CREATE TABLE `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `slug` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `short_description` text DEFAULT NULL,
            `price` decimal(10,2) NOT NULL,
            `sale_price` decimal(10,2) DEFAULT NULL,
            `sku` varchar(100) DEFAULT NULL,
            `stock_quantity` int(11) DEFAULT 0,
            `category_id` int(11) DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `featured` tinyint(1) DEFAULT 0,
            `status` enum('active','inactive','draft') DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `slug` (`slug`),
            KEY `category_id` (`category_id`),
            CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Products table created</p>";
    
    // Users table
    $pdo->exec("
        CREATE TABLE `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `email` varchar(255) NOT NULL,
            `password` varchar(255) NOT NULL,
            `first_name` varchar(100) NOT NULL,
            `last_name` varchar(100) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email_verified` tinyint(1) DEFAULT 0,
            `status` enum('active','inactive','suspended') DEFAULT 'active',
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Users table created</p>";
    
    // Cart table
    $pdo->exec("
        CREATE TABLE `cart` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `session_id` varchar(255) DEFAULT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` int(11) NOT NULL DEFAULT 1,
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `product_id` (`product_id`),
            CONSTRAINT `cart_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
            CONSTRAINT `cart_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Cart table created</p>";
    
    // Wishlist table
    $pdo->exec("
        CREATE TABLE `wishlist` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            PRIMARY KEY (`id`),
            UNIQUE KEY `user_product` (`user_id`, `product_id`),
            KEY `product_id` (`product_id`),
            CONSTRAINT `wishlist_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
            CONSTRAINT `wishlist_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "<p style='color: green;'>✓ Wishlist table created</p>";
    
    echo "<h2>Step 4: Inserting Sample Data</h2>";
    
    // Insert categories
    $categories = [
        ['Electronics', 'electronics', 'Electronic devices and gadgets'],
        ['Cosmetics', 'cosmetics', 'Beauty and cosmetic products'],
        ['Sports', 'sports', 'Sports and fitness equipment'],
        ['Food & Snacks', 'food-snacks', 'Food items and snacks'],
        ['Health & Medicine', 'health-medicine', 'Health and medical products'],
        ['Vegetables', 'vegetables', 'Fresh vegetables and produce']
    ];
    
    foreach ($categories as $cat) {
        $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)");
        $stmt->execute($cat);
    }
    echo "<p style='color: green;'>✓ 6 categories inserted</p>";
    
    // Insert products
    $products = [
        // Electronics
        ['iPhone 14 Pro', 'iphone-14-pro', 'Latest iPhone with advanced camera system', 'Premium smartphone with excellent camera', 15000000, 14000000, 'IP14PRO001', 10, 1, 'default-product.jpg', 1],
        ['Samsung Galaxy S23', 'samsung-galaxy-s23', 'Flagship Android smartphone', 'High-performance Android device', 12000000, NULL, 'SGS23001', 15, 1, 'default-product.jpg', 1],
        ['MacBook Air M2', 'macbook-air-m2', 'Ultra-thin laptop with M2 chip', 'Lightweight laptop with powerful M2 chip', 18000000, 17000000, 'MBA2001', 8, 1, 'default-product.jpg', 1],
        ['Sony Headphones WH-1000XM4', 'sony-headphones-wh1000xm4', 'Premium noise-canceling headphones', 'Industry-leading noise canceling', 4500000, 4200000, 'SH1000001', 15, 1, 'default-product.jpg', 1],
        ['iPad Air 5th Gen', 'ipad-air-5th-gen', 'Powerful tablet with M1 chip', 'Powerful tablet with beautiful display', 8500000, NULL, 'IPA5001', 12, 1, 'default-product.jpg', 0],
        
        // Cosmetics
        ['Serum Vitamin C 20%', 'serum-vitamin-c-20', 'Anti-aging vitamin C serum', 'Brightening and anti-aging serum', 250000, 200000, 'SVC20001', 50, 2, 'default-product.jpg', 1],
        ['Foundation Liquid Natural', 'foundation-liquid-natural', 'Full-coverage liquid foundation', 'Long-lasting foundation with natural finish', 320000, 280000, 'FLN001', 40, 2, 'default-product.jpg', 1],
        ['Skincare Set Complete', 'skincare-set-complete', 'Complete skincare routine set', 'Complete skincare for glowing skin', 650000, 580000, 'SSC001', 20, 2, 'default-product.jpg', 1],
        ['BB Cream SPF 50', 'bb-cream-spf-50', 'Multi-functional BB cream with SPF', 'All-in-one BB cream with sun protection', 180000, 160000, 'BBC50001', 25, 2, 'default-product.jpg', 1],
        ['Mascara Waterproof Black', 'mascara-waterproof-black', 'Waterproof mascara for dramatic lashes', 'Waterproof mascara for all-day wear', 180000, NULL, 'MWB001', 35, 2, 'default-product.jpg', 0],
        
        // Sports
        ['Dumbbell Set 20kg', 'dumbbell-set-20kg', 'Professional adjustable dumbbell set', 'Adjustable weight set for home gym', 800000, 750000, 'DS20001', 5, 3, 'default-product.jpg', 1],
        ['Treadmill Electric Home', 'treadmill-electric-home', 'Professional electric treadmill', 'Electric treadmill with workout programs', 12000000, 11000000, 'TEH001', 3, 3, 'default-product.jpg', 1],
        ['Running Shoes Nike', 'running-shoes-nike', 'Lightweight running shoes', 'Comfortable running shoes with cushioning', 1200000, 1050000, 'RSN001', 18, 3, 'default-product.jpg', 1],
        ['Yoga Mat Premium', 'yoga-mat-premium', 'High-quality non-slip yoga mat', 'Non-slip premium yoga mat', 300000, NULL, 'YMP001', 20, 3, 'default-product.jpg', 0],
        ['Protein Powder Whey', 'protein-powder-whey', 'High-quality whey protein powder', 'Premium whey protein for muscle building', 450000, 400000, 'PPW001', 30, 3, 'default-product.jpg', 0],
        
        // Food & Snacks
        ['Premium Coffee Beans', 'premium-coffee-beans', 'Single-origin arabica coffee beans', 'Premium coffee beans with rich flavor', 180000, 160000, 'PCB001', 50, 4, 'default-product.jpg', 1],
        ['Oreo Cookies', 'oreo-cookies', 'Classic chocolate sandwich cookies', 'Classic chocolate sandwich cookies', 25000, 22000, 'ORC001', 80, 4, 'default-product.jpg', 1],
        ['Chocolate Dark Premium', 'chocolate-dark-premium', 'Premium dark chocolate 70% cocoa', 'Premium dark chocolate with rich flavor', 85000, NULL, 'CDP001', 40, 4, 'default-product.jpg', 0],
        ['Honey Pure Natural', 'honey-pure-natural', 'Pure natural honey from local beekeepers', 'Pure natural honey with health benefits', 120000, 100000, 'HPN001', 35, 4, 'default-product.jpg', 1],
        ['Chitato Snack', 'chitato-snack', 'Popular Indonesian potato chips', 'Crispy potato chips with Indonesian taste', 15000, NULL, 'CHS001', 100, 4, 'default-product.jpg', 0],
        
        // Health & Medicine
        ['Multivitamin Complete', 'multivitamin-complete', 'Complete multivitamin supplement', 'Complete daily multivitamin supplement', 150000, 130000, 'MVC001', 60, 5, 'default-product.jpg', 1],
        ['Vitamin C Tablets', 'vitamin-c-tablets', 'High-potency vitamin C supplement', 'Immune-boosting vitamin C supplement', 120000, 100000, 'VCT001', 40, 5, 'default-product.jpg', 0],
        ['Fish Oil Omega-3', 'fish-oil-omega3', 'High-quality fish oil supplement', 'Omega-3 fish oil for heart health', 200000, NULL, 'FO3001', 40, 5, 'default-product.jpg', 0],
        
        // Vegetables
        ['Fresh Broccoli', 'fresh-broccoli', 'Nutritious fresh broccoli', 'Fresh organic broccoli rich in nutrients', 12000, 10000, 'FB001', 150, 6, 'default-product.jpg', 1],
        ['Fresh Tomatoes', 'fresh-tomatoes', 'Fresh, ripe tomatoes', 'Fresh organic tomatoes for cooking', 8000, NULL, 'FT001', 200, 6, 'default-product.jpg', 0],
        ['Carrots Organic Bundle', 'carrots-organic-bundle', 'Fresh organic carrots bundle', 'Fresh organic carrots rich in beta-carotene', 18000, 15000, 'COB001', 80, 6, 'default-product.jpg', 1],
        ['Organic Spinach Fresh', 'organic-spinach-fresh', 'Fresh organic spinach leaves', 'Fresh organic spinach rich in nutrients', 15000, NULL, 'OSF001', 100, 6, 'default-product.jpg', 0],
        ['Cucumber Fresh Organic', 'cucumber-fresh-organic', 'Fresh organic cucumbers', 'Fresh organic cucumbers for salads', 12000, 10000, 'CFO001', 90, 6, 'default-product.jpg', 0]
    ];
    
    $product_count = 0;
    foreach ($products as $product) {
        $stmt = $pdo->prepare("
            INSERT INTO products (name, slug, description, short_description, price, sale_price, sku, stock_quantity, category_id, image, featured) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($product);
        $product_count++;
    }
    echo "<p style='color: green;'>✓ $product_count products inserted</p>";
    
    // Insert test users
    $password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // user123
    $users = [
        ['<EMAIL>', 'Test', 'User'],
        ['<EMAIL>', 'John', 'Doe'],
        ['<EMAIL>', 'Jane', 'Smith']
    ];
    
    foreach ($users as $user) {
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, first_name, last_name, phone, email_verified, status) 
            VALUES (?, ?, ?, ?, '081234567890', 1, 'active')
        ");
        $stmt->execute([$user[0], $password_hash, $user[1], $user[2]]);
    }
    echo "<p style='color: green;'>✓ 3 test users created (password: user123)</p>";
    
    // Create upload directories
    echo "<h2>Step 5: Directory Setup</h2>";
    $directories = ['uploads', 'uploads/products', 'uploads/categories'];
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "<p style='color: green;'>✓ Created: $dir</p>";
        }
    }
    
    // Create default product image
    if (!file_exists('uploads/products/default-product.jpg')) {
        $default_image = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        file_put_contents('uploads/products/default-product.jpg', $default_image);
        echo "<p style='color: green;'>✓ Created default product image</p>";
    }
    
    // Final verification
    echo "<h2>Step 6: Final Verification</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    $total_products = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM categories WHERE status = 'active'");
    $total_categories = $stmt->fetch()['total'];
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2 style='color: #155724;'>🎉 DATABASE SETUP COMPLETE!</h2>";
    echo "<p><strong>Successfully Created:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database: db_tewuneed2</li>";
    echo "<li>✅ Products: $total_products active products</li>";
    echo "<li>✅ Categories: $total_categories active categories</li>";
    echo "<li>✅ Users: 3 test accounts</li>";
    echo "<li>✅ Upload directories</li>";
    echo "</ul>";
    
    echo "<p><strong>Test Your Website Now:</strong></p>";
    echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🛍️ View Products</a></p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Home Page</a></p>";
    echo "<p><a href='login_firebase.php' style='background: #ffc107; color: black; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 Firebase Login</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2 style='color: #721c24;'>❌ SETUP ERROR</h2>";
    echo "<p style='color: #721c24;'><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Please ensure:</strong></p>";
    echo "<ul>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>No other applications are using the database</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
?>
