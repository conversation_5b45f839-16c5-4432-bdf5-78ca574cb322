<?php
require_once 'config/config.php';

$order_number = $_GET['order'] ?? '';

if (empty($order_number)) {
    $_SESSION['error'] = 'Order not found.';
    redirect('index.php');
}

try {
    $pdo = getDBConnection();
    
    // Get order details
    $stmt = $pdo->prepare("
        SELECT o.*, u.first_name, u.last_name, u.email 
        FROM orders o 
        LEFT JOIN users u ON o.user_id = u.id 
        WHERE o.order_number = ?
    ");
    $stmt->execute([$order_number]);
    $order = $stmt->fetch();
    
    if (!$order) {
        $_SESSION['error'] = 'Order not found.';
        redirect('index.php');
    }
    
    // Check if user owns this order (if logged in)
    if (isset($_SESSION['user_id']) && $order['user_id'] != $_SESSION['user_id']) {
        $_SESSION['error'] = 'Access denied.';
        redirect('my-orders.php');
    }
    
    // Get order items
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, p.image, p.slug
        FROM order_items oi 
        LEFT JOIN products p ON oi.product_id = p.id 
        WHERE oi.order_id = ?
    ");
    $stmt->execute([$order['id']]);
    $order_items = $stmt->fetchAll();
    
} catch (Exception $e) {
    $_SESSION['error'] = 'Error loading order details.';
    redirect('index.php');
}

$page_title = 'Order Details - ' . $order['order_number'];
include 'includes/header.php';
?>

<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>Order Details</h2>
                    <p class="text-muted mb-0">Order #<?php echo htmlspecialchars($order['order_number']); ?></p>
                </div>
                <div>
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="my-orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Orders
                        </a>
                    <?php endif; ?>
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>Print
                    </button>
                </div>
            </div>
            
            <!-- Order Status -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <i class="fas fa-shopping-cart fa-2x text-<?php 
                                    echo match($order['status']) {
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'shipped' => 'primary',
                                        'delivered' => 'success',
                                        'cancelled' => 'danger',
                                        default => 'secondary'
                                    };
                                ?>"></i>
                            </div>
                            <h6>Order Status</h6>
                            <span class="badge bg-<?php 
                                echo match($order['status']) {
                                    'pending' => 'warning',
                                    'processing' => 'info',
                                    'shipped' => 'primary',
                                    'delivered' => 'success',
                                    'cancelled' => 'danger',
                                    default => 'secondary'
                                };
                            ?> fs-6">
                                <?php echo ucfirst($order['status']); ?>
                            </span>
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <i class="fas fa-credit-card fa-2x text-<?php 
                                    echo match($order['payment_status']) {
                                        'pending' => 'warning',
                                        'paid' => 'success',
                                        'failed' => 'danger',
                                        'refunded' => 'secondary',
                                        default => 'secondary'
                                    };
                                ?>"></i>
                            </div>
                            <h6>Payment Status</h6>
                            <span class="badge bg-<?php 
                                echo match($order['payment_status']) {
                                    'pending' => 'warning',
                                    'paid' => 'success',
                                    'failed' => 'danger',
                                    'refunded' => 'secondary',
                                    default => 'secondary'
                                };
                            ?> fs-6">
                                <?php echo ucfirst($order['payment_status']); ?>
                            </span>
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <i class="fas fa-calendar fa-2x text-info"></i>
                            </div>
                            <h6>Order Date</h6>
                            <p class="mb-0"><?php echo formatDate($order['created_at']); ?></p>
                        </div>
                        
                        <div class="col-md-3 text-center">
                            <div class="mb-2">
                                <i class="fas fa-money-bill fa-2x text-success"></i>
                            </div>
                            <h6>Total Amount</h6>
                            <h5 class="text-primary mb-0"><?php echo formatPrice($order['total_amount']); ?></h5>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Order Items -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-bag me-2"></i>Order Items
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($order_items as $item): ?>
                                <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                                    <img src="uploads/<?php echo $item['image'] ?: 'default-product.jpg'; ?>" 
                                         alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                         class="rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <?php if ($item['slug']): ?>
                                                <a href="product.php?slug=<?php echo $item['slug']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($item['product_name']); ?>
                                                </a>
                                            <?php else: ?>
                                                <?php echo htmlspecialchars($item['product_name']); ?>
                                            <?php endif; ?>
                                        </h6>
                                        <p class="text-muted mb-1">Quantity: <?php echo $item['quantity']; ?></p>
                                        <p class="text-muted mb-0">Unit Price: <?php echo formatPrice($item['price']); ?></p>
                                    </div>
                                    <div class="text-end">
                                        <strong><?php echo formatPrice($item['total']); ?></strong>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <!-- Order Summary -->
                            <div class="row mt-3">
                                <div class="col-md-6 offset-md-6">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Subtotal:</span>
                                        <span><?php echo formatPrice($order['total_amount'] - $order['shipping_amount'] - $order['tax_amount'] + $order['discount_amount']); ?></span>
                                    </div>
                                    
                                    <?php if ($order['shipping_amount'] > 0): ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Shipping:</span>
                                            <span><?php echo formatPrice($order['shipping_amount']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($order['tax_amount'] > 0): ?>
                                        <div class="d-flex justify-content-between mb-2">
                                            <span>Tax:</span>
                                            <span><?php echo formatPrice($order['tax_amount']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($order['discount_amount'] > 0): ?>
                                        <div class="d-flex justify-content-between mb-2 text-success">
                                            <span>Discount:</span>
                                            <span>-<?php echo formatPrice($order['discount_amount']); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <strong>Total:</strong>
                                        <strong class="text-primary"><?php echo formatPrice($order['total_amount']); ?></strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Order Info Sidebar -->
                <div class="col-lg-4">
                    <!-- Shipping Address -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-truck me-2"></i>Shipping Address
                            </h6>
                        </div>
                        <div class="card-body">
                            <address class="mb-0">
                                <?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?>
                            </address>
                        </div>
                    </div>
                    
                    <!-- Payment Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-credit-card me-2"></i>Payment Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Payment Method:</strong><br>
                                <?php echo htmlspecialchars($order['payment_method']); ?>
                            </div>
                            <div class="mb-2">
                                <strong>Payment Status:</strong><br>
                                <span class="badge bg-<?php 
                                    echo match($order['payment_status']) {
                                        'pending' => 'warning',
                                        'paid' => 'success',
                                        'failed' => 'danger',
                                        'refunded' => 'secondary',
                                        default => 'secondary'
                                    };
                                ?>">
                                    <?php echo ucfirst($order['payment_status']); ?>
                                </span>
                            </div>
                            
                            <?php if ($order['payment_status'] === 'pending'): ?>
                                <div class="mt-3">
                                    <a href="order-confirmation.php?order=<?php echo $order['order_number']; ?>" 
                                       class="btn btn-warning w-100">
                                        <i class="fas fa-credit-card me-2"></i>Complete Payment
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Order Notes -->
                    <?php if ($order['notes']): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-sticky-note me-2"></i>Order Notes
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Order Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Order Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <?php if (in_array($order['status'], ['pending', 'processing'])): ?>
                                    <button class="btn btn-outline-danger" 
                                            onclick="cancelOrder('<?php echo $order['order_number']; ?>')">
                                        <i class="fas fa-times me-2"></i>Cancel Order
                                    </button>
                                <?php endif; ?>
                                
                                <a href="mailto:<EMAIL>?subject=Order <?php echo $order['order_number']; ?>" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-envelope me-2"></i>Contact Support
                                </a>
                                
                                <?php if ($order['status'] === 'delivered'): ?>
                                    <button class="btn btn-outline-success" onclick="leaveReview()">
                                        <i class="fas fa-star me-2"></i>Leave Review
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function cancelOrder(orderNumber) {
    if (confirm('Are you sure you want to cancel this order?')) {
        fetch('ajax/cancel-order.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_number: orderNumber
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order cancelled successfully');
                location.reload();
            } else {
                alert(data.message || 'Error cancelling order');
            }
        })
        .catch(error => {
            alert('Error cancelling order');
        });
    }
}

function leaveReview() {
    alert('Review feature will be implemented soon!');
}
</script>

<?php include 'includes/footer.php'; ?>
