<?php
require_once 'config/config.php';

$page_title = 'My Orders - Debug';
$page_description = 'Debug version of My Orders page';

echo "<!DOCTYPE html>
<html>
<head>
    <title>My Orders Debug</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='mb-4'>🔍 My Orders Debug Page</h1>";

// Debug session information
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h4 class='mb-0'>Session Debug Information</h4>";
echo "</div>";
echo "<div class='card-body'>";
echo "<h6>Session ID: " . session_id() . "</h6>";
echo "<h6>Session Data:</h6>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";
echo "</div>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<div class='alert alert-warning'>";
    echo "<h4>❌ Not Logged In</h4>";
    echo "<p>You need to login first to access My Orders page.</p>";
    echo "<a href='test-login-and-orders.php' class='btn btn-primary'>Go to Login Test</a>";
    echo "</div>";
    echo "</div></body></html>";
    exit;
}

$user_id = $_SESSION['user_id'];

echo "<div class='alert alert-success'>";
echo "<h4>✅ Logged In Successfully</h4>";
echo "<p>User ID: $user_id</p>";
echo "</div>";

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$where_conditions = ['o.user_id = ?'];
$params = [$user_id];

if (!empty($status_filter)) {
    $where_conditions[] = 'o.status = ?';
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = '(o.order_number LIKE ? OR p.name LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h4 class='mb-0'>Query Debug Information</h4>";
echo "</div>";
echo "<div class='card-body'>";
echo "<h6>Where Clause: <code>$where_clause</code></h6>";
echo "<h6>Parameters: <code>" . implode(', ', $params) . "</code></h6>";
echo "</div>";
echo "</div>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='alert alert-success'>✅ Database connection successful</div>";
    
    // Get orders with item count
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as item_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        {$where_clause}
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-secondary text-white'>";
    echo "<h4 class='mb-0'>SQL Query</h4>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<pre>" . htmlspecialchars($sql) . "</pre>";
    echo "</div>";
    echo "</div>";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    echo "<div class='alert alert-info'>";
    echo "<h4>📊 Query Results</h4>";
    echo "<p>Found " . count($orders) . " orders for user ID: $user_id</p>";
    echo "</div>";
    
    if (!empty($orders)) {
        echo "<div class='card mb-4'>";
        echo "<div class='card-header bg-success text-white'>";
        echo "<h4 class='mb-0'>Orders Found</h4>";
        echo "</div>";
        echo "<div class='card-body'>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Order Number</th>";
        echo "<th>Status</th>";
        echo "<th>Total</th>";
        echo "<th>Items</th>";
        echo "<th>Products</th>";
        echo "<th>Created</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td><span class='badge bg-primary'>" . htmlspecialchars($order['status']) . "</span></td>";
            echo "<td>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td>" . $order['item_count'] . "</td>";
            echo "<td>" . htmlspecialchars($order['product_names'] ?? 'No products') . "</td>";
            echo "<td>" . formatDate($order['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<h4>⚠️ No Orders Found</h4>";
        echo "<p>No orders found for user ID: $user_id</p>";
        echo "</div>";
        
        // Check if orders exist for this user in database
        $check_sql = "SELECT COUNT(*) FROM orders WHERE user_id = ?";
        $check_stmt = $pdo->prepare($check_sql);
        $check_stmt->execute([$user_id]);
        $order_count = $check_stmt->fetchColumn();
        
        echo "<div class='alert alert-info'>";
        echo "<h4>🔍 Direct Order Count Check</h4>";
        echo "<p>Direct count query found: <strong>$order_count</strong> orders for user ID: $user_id</p>";
        echo "</div>";
        
        if ($order_count > 0) {
            // Get raw orders
            $raw_sql = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC";
            $raw_stmt = $pdo->prepare($raw_sql);
            $raw_stmt->execute([$user_id]);
            $raw_orders = $raw_stmt->fetchAll();
            
            echo "<div class='card mb-4'>";
            echo "<div class='card-header bg-warning text-dark'>";
            echo "<h4 class='mb-0'>Raw Orders Data</h4>";
            echo "</div>";
            echo "<div class='card-body'>";
            echo "<pre>" . print_r($raw_orders, true) . "</pre>";
            echo "</div>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Database Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

// Test database tables
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-dark text-white'>";
echo "<h4 class='mb-0'>Database Tables Check</h4>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $tables = ['users', 'orders', 'order_items', 'products'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "<p><strong>$table:</strong> $count records</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>Error checking tables: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";
echo "</div>";

echo "<div class='text-center'>";
echo "<h4 class='mb-4'>Actions</h4>";
echo "<a href='my-orders.php' class='btn btn-primary me-2'>Try Regular My Orders Page</a>";
echo "<a href='test-login-and-orders.php' class='btn btn-success me-2'>Login Test Page</a>";
echo "<a href='fix-my-orders.php' class='btn btn-warning'>Fix Database</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
