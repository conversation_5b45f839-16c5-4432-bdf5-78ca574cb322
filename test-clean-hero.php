<?php
require_once 'config/config.php';

$page_title = 'Clean Hero Test';
$page_description = 'Testing clean hero section without stray code';

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-success">✅ Clean Hero Section Test</h1>
        <p class="lead text-gray-600">Testing the hero section without any stray code</p>
    </div>

    <!-- Status Check -->
    <div class="row g-4 mb-5">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-check fa-lg text-white"></i>
                    </div>
                    <h5 class="fw-bold">SVG Pattern Removed</h5>
                    <p class="text-muted small">Removed complex SVG pattern that was causing display issues</p>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-code fa-lg text-white"></i>
                    </div>
                    <h5 class="fw-bold">Clean HTML</h5>
                    <p class="text-muted small">All code is properly contained and no stray characters</p>
                </div>
            </div>
        </div>
    </div>

    <!-- What Was Fixed -->
    <div class="card border-0 shadow-sm mb-5">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">🔧 What Was Fixed</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Removed Problematic Code</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-times-circle text-danger me-2"></i>
                            Complex SVG pattern in hero background
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times-circle text-danger me-2"></i>
                            URL-encoded SVG data causing display issues
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-times-circle text-danger me-2"></i>
                            Potential character encoding problems
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Replaced With</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Simple CSS gradient background
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            Clean, standard CSS colors
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            No complex patterns or encodings
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Clean Hero -->
    <div class="card border-0 shadow-sm mb-5">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">🎨 Clean Hero Section Preview</h4>
        </div>
        <div class="card-body p-0">
            <!-- Mini Hero Section for Testing -->
            <section class="bg-primary text-white position-relative overflow-hidden py-4">
                <div class="position-absolute w-100 h-100" style="background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);"></div>
                
                <div class="container-lg position-relative">
                    <div class="row align-items-center py-3">
                        <div class="col-lg-8">
                            <h3 class="fw-bold mb-2 text-white">
                                Welcome to <span class="text-warning">TeWuNeed</span>
                            </h3>
                            <p class="mb-3" style="color: rgba(255,255,255,0.9);">
                                Clean hero section without any stray code or complex patterns.
                            </p>
                            <div class="d-flex gap-3">
                                <a href="#" class="btn btn-warning">Shop Now</a>
                                <a href="#" class="btn btn-outline-light">Learn More</a>
                            </div>
                        </div>
                        <div class="col-lg-4 text-center">
                            <div class="bg-white bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 100px; height: 100px;">
                                <i class="fas fa-gem fa-3x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Before vs After -->
    <div class="card border-0 shadow-sm mb-5">
        <div class="card-header">
            <h4 class="mb-0">📊 Before vs After</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3 text-danger">❌ Before (Problematic)</h6>
                    <div class="bg-light p-3 rounded">
                        <code style="font-size: 0.8rem; word-break: break-all;">
                            &lt;div class="hero-pattern" style="background-image: url('data:image/svg+xml,&lt;svg width="60" height="60"...');"&gt;&lt;/div&gt;
                        </code>
                    </div>
                    <ul class="mt-3 small">
                        <li>Complex SVG data URL</li>
                        <li>Special characters causing issues</li>
                        <li>Potential encoding problems</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3 text-success">✅ After (Clean)</h6>
                    <div class="bg-light p-3 rounded">
                        <code style="font-size: 0.8rem;">
                            &lt;div style="background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);"&gt;&lt;/div&gt;
                        </code>
                    </div>
                    <ul class="mt-3 small">
                        <li>Simple CSS gradient</li>
                        <li>Standard hex colors</li>
                        <li>No special characters</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Instructions -->
    <div class="alert alert-info">
        <h5 class="alert-heading">
            <i class="fas fa-info-circle me-2"></i>Testing Instructions
        </h5>
        <ol class="mb-0">
            <li>Go to the homepage: <a href="<?php echo SITE_URL; ?>/index.php" target="_blank">index.php</a></li>
            <li>Check if the hero section displays cleanly without any stray code</li>
            <li>Look for any visible code or strange characters</li>
            <li>Verify the blue gradient background appears properly</li>
            <li>Ensure all text and buttons are visible and working</li>
        </ol>
    </div>

    <!-- Quick Actions -->
    <div class="text-center">
        <h4 class="mb-4">Test the Fixed Homepage</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/index.php" class="btn btn-primary btn-lg">
                <i class="fas fa-home me-2"></i>Test Homepage
            </a>
            <a href="<?php echo SITE_URL; ?>/test-clean-page.php" class="btn btn-success btn-lg">
                <i class="fas fa-check me-2"></i>Clean Page Test
            </a>
            <a href="<?php echo SITE_URL; ?>/test-modern-design.php" class="btn btn-info btn-lg">
                <i class="fas fa-palette me-2"></i>Design Test
            </a>
        </div>
        
        <div class="mt-4">
            <div class="alert alert-success">
                <h6 class="alert-heading">
                    <i class="fas fa-check-circle me-2"></i>Hero Section Fixed!
                </h6>
                <p class="mb-0">
                    The problematic SVG pattern has been removed and replaced with a clean CSS gradient. 
                    Your homepage should now display without any stray code in the hero section.
                </p>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
