<?php
require_once 'config/config.php';

echo "<h2>Setup Order Test</h2>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5;'>";

try {
    $pdo = getDBConnection();
    
    // Step 1: Clear existing cart
    $session_id = session_id();
    if (isset($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    } else {
        $stmt = $pdo->prepare("DELETE FROM cart WHERE session_id = ? AND user_id IS NULL");
        $stmt->execute([$session_id]);
    }
    echo "<p style='color: green;'>✅ Cart cleared</p>";
    
    // Step 2: Add a test product to cart
    $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
    $test_product = $stmt->fetch();
    
    if ($test_product) {
        if (isset($_SESSION['user_id'])) {
            $stmt = $pdo->prepare("INSERT INTO cart (user_id, product_id, quantity, created_at) VALUES (?, ?, 2, NOW())");
            $stmt->execute([$_SESSION['user_id'], $test_product['id']]);
        } else {
            $stmt = $pdo->prepare("INSERT INTO cart (session_id, product_id, quantity, created_at) VALUES (?, ?, 2, NOW())");
            $stmt->execute([$session_id, $test_product['id']]);
        }
        echo "<p style='color: green;'>✅ Added test product to cart: " . htmlspecialchars($test_product['name']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ No active products found</p>";
    }
    
    // Step 3: Set up checkout data
    $_SESSION['checkout_data'] = [
        'first_name' => 'Test',
        'last_name' => 'Customer',
        'email' => '<EMAIL>',
        'phone' => '+***********',
        'address_line_1' => 'Test Address 123',
        'address_line_2' => '',
        'city' => 'Jakarta',
        'state' => 'DKI Jakarta',
        'postal_code' => '12345',
        'country' => 'Indonesia',
        'payment_method' => 'bank_transfer',
        'notes' => 'Test order from setup script',
        'coupon_code' => '',
        'discount_amount' => 0,
        'coupon_id' => null
    ];
    echo "<p style='color: green;'>✅ Checkout data prepared</p>";
    
    // Step 4: Display current status
    echo "<h3>Current Status</h3>";
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>User ID:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Guest') . "</p>";
    
    // Check cart count
    if (isset($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cart WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    } else {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cart WHERE session_id = ? AND user_id IS NULL");
        $stmt->execute([$session_id]);
    }
    $cart_count = $stmt->fetch()['count'];
    echo "<p><strong>Cart Items:</strong> $cart_count</p>";
    
    echo "<p><strong>Checkout Data:</strong> " . (isset($_SESSION['checkout_data']) ? 'Available' : 'Missing') . "</p>";
    
    echo "<h3>Test Links</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='order-review.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Order Review</a>";
    echo "<a href='order-review.php?debug=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Order Review (Debug)</a>";
    echo "<a href='cart.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>View Cart</a>";
    echo "<a href='checkout.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Checkout</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<p style='margin-top: 30px;'><a href='index.php' style='color: #0d6efd;'>← Back to Home</a></p>";
echo "</div>";
?>
