<?php
// Fix Status Column - Emergency Database Repair
require_once 'config/config.php';

echo "<h1>🔧 Emergency Database Repair</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;'>";

try {
    $pdo = getDBConnection();
    
    echo "<h2>🔍 Checking Database Structure...</h2>";
    
    // Check if status column exists in products table
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $has_status = in_array('status', $columns);
    
    if (!$has_status) {
        echo "<p style='color: orange;'>⚠️ Status column missing in products table. Adding now...</p>";
        
        // Add status column to products table
        $pdo->exec("ALTER TABLE products ADD COLUMN status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active' AFTER stock_quantity");
        
        // Update all existing products to active status
        $pdo->exec("UPDATE products SET status = 'active' WHERE status IS NULL");
        
        echo "<p style='color: green;'>✅ Status column added to products table</p>";
    } else {
        echo "<p style='color: green;'>✅ Status column already exists in products table</p>";
    }
    
    // Check if status column exists in categories table
    $stmt = $pdo->query("DESCRIBE categories");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $has_cat_status = in_array('status', $columns);
    
    if (!$has_cat_status) {
        echo "<p style='color: orange;'>⚠️ Status column missing in categories table. Adding now...</p>";
        
        // Add status column to categories table
        $pdo->exec("ALTER TABLE categories ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active' AFTER description");
        
        // Update all existing categories to active status
        $pdo->exec("UPDATE categories SET status = 'active' WHERE status IS NULL");
        
        echo "<p style='color: green;'>✅ Status column added to categories table</p>";
    } else {
        echo "<p style='color: green;'>✅ Status column already exists in categories table</p>";
    }
    
    // Verify the fix
    echo "<h2>🧪 Testing Database Queries...</h2>";
    
    // Test products query
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    $product_count = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Active products query works: $product_count products found</p>";
    
    // Test categories query
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM categories WHERE status = 'active'");
    $category_count = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Active categories query works: $category_count categories found</p>";
    
    // Test join query (the one that was failing)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
    ");
    $join_count = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Products-Categories join query works: $join_count products found</p>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>🎉 Database Repair Complete!</h3>";
    echo "<p style='color: #155724; margin: 0;'>All status columns have been added and your database is now fully functional.</p>";
    echo "</div>";
    
    echo "<h2>🚀 Quick Actions</h2>";
    echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
    echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛍️ Test Products Page</a>";
    echo "<a href='test_complete_website.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Run Full Test</a>";
    echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Home Page</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ Database Error</h3>";
    echo "<p style='color: #721c24; margin: 0;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p style='color: #721c24; margin: 10px 0 0 0;'>Please ensure XAMPP is running and MySQL service is started.</p>";
    echo "</div>";
    
    echo "<h3>🔧 Manual Fix Instructions:</h3>";
    echo "<ol>";
    echo "<li>Open phpMyAdmin (http://localhost/phpmyadmin)</li>";
    echo "<li>Select your database (tewuneed2 or db_tewuneed)</li>";
    echo "<li>Go to SQL tab and run these commands:</li>";
    echo "</ol>";
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0;'>";
    echo "<pre style='margin: 0;'>";
    echo "ALTER TABLE products ADD COLUMN status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active';\n";
    echo "UPDATE products SET status = 'active';\n";
    echo "ALTER TABLE categories ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active';\n";
    echo "UPDATE categories SET status = 'active';";
    echo "</pre>";
    echo "</div>";
}

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    background: #f8f9fa;
    margin: 0;
    padding: 20px;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}
</style>
