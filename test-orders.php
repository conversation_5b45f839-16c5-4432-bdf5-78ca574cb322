<?php
require_once 'config/config.php';

echo "<h1>Order Database Test</h1>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check if orders table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Orders table exists</p>";
        
        // Get recent orders
        $stmt = $pdo->query("
            SELECT o.*, 
                   COUNT(oi.id) as item_count,
                   CASE 
                       WHEN o.user_id IS NOT NULL THEN CONCAT(u.first_name, ' ', u.last_name)
                       ELSE JSON_UNQUOTE(JSON_EXTRACT(o.customer_info, '$.first_name'))
                   END as customer_name
            FROM orders o 
            LEFT JOIN users u ON o.user_id = u.id 
            LEFT JOIN order_items oi ON o.id = oi.order_id
            GROUP BY o.id
            ORDER BY o.created_at DESC 
            LIMIT 10
        ");
        $orders = $stmt->fetchAll();
        
        echo "<h2>Recent Orders (" . count($orders) . " found)</h2>";
        
        if (count($orders) > 0) {
            echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>Order Number</th><th>Customer</th><th>Items</th><th>Total</th><th>Status</th><th>Payment</th><th>Date</th><th>Actions</th>";
            echo "</tr>";
            
            foreach ($orders as $order) {
                echo "<tr>";
                echo "<td>" . $order['id'] . "</td>";
                echo "<td><strong>" . htmlspecialchars($order['order_number']) . "</strong></td>";
                echo "<td>" . htmlspecialchars($order['customer_name'] ?: 'Guest') . "</td>";
                echo "<td>" . $order['item_count'] . " items</td>";
                echo "<td>Rp " . number_format($order['total_amount'], 0, ',', '.') . "</td>";
                echo "<td><span style='background: orange; color: white; padding: 2px 8px; border-radius: 3px;'>" . ucfirst($order['status']) . "</span></td>";
                echo "<td><span style='background: blue; color: white; padding: 2px 8px; border-radius: 3px;'>" . ucfirst($order['payment_status']) . "</span></td>";
                echo "<td>" . date('d M Y H:i', strtotime($order['created_at'])) . "</td>";
                echo "<td>";
                echo "<a href='admin/order-detail.php?id=" . $order['id'] . "' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin-right: 5px;'>Admin View</a>";
                echo "<a href='order-confirmation.php?order=" . $order['order_number'] . "' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Customer View</a>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠ No orders found in database</p>";
            echo "<p>Try placing a test order to see if it gets saved.</p>";
        }
        
        // Check order_items table
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM order_items");
        $item_count = $stmt->fetch()['count'];
        echo "<p>Order items in database: <strong>" . $item_count . "</strong></p>";
        
    } else {
        echo "<p style='color: red;'>✗ Orders table does not exist</p>";
    }
    
    // Check if order_items table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'order_items'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Order items table exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Order items table does not exist</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Quick Links</h2>";
echo "<p><a href='order-review.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Place Test Order</a></p>";
echo "<p><a href='admin/orders.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Admin Orders</a></p>";
echo "<p><a href='admin/index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Admin Dashboard</a></p>";

echo "<hr>";
echo "<h2>Session Info</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>User ID: " . ($_SESSION['user_id'] ?? 'Not logged in') . "</p>";
echo "<p>Admin ID: " . ($_SESSION['admin_id'] ?? 'Not admin') . "</p>";
echo "<p>Checkout Data: " . (isset($_SESSION['checkout_data']) ? 'Present' : 'Not present') . "</p>";
?>
