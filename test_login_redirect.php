<?php
// Test Login and Redirect Functionality
require_once 'config/config.php';

echo "<h1>🔍 Login & Redirect Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;'>";

// Check current session
echo "<h2>1. Current Session Status</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session Status: " . session_status() . "</p>";

if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✓ User is logged in:</p>";
    echo "<ul>";
    echo "<li>User ID: " . $_SESSION['user_id'] . "</li>";
    echo "<li>User Email: " . $_SESSION['user_email'] . "</li>";
    echo "<li>User Name: " . $_SESSION['user_name'] . "</li>";
    echo "</ul>";
    
    echo "<p><a href='index.php' class='btn btn-primary'>Go to Home Page</a></p>";
    echo "<p><a href='?logout=1' class='btn btn-warning'>Logout</a></p>";
} else {
    echo "<p style='color: orange;'>⚠ No user logged in</p>";
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<p style='color: blue;'>→ Logged out. <a href='login.php'>Login again</a></p>";
}

// Test login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    echo "<h2>2. Login Test Results</h2>";
    echo "<p>Email: " . htmlspecialchars($email) . "</p>";
    echo "<p>Password: " . str_repeat('*', strlen($password)) . "</p>";
    
    try {
        $pdo = getDBConnection();
        echo "<p style='color: green;'>✓ Database connected</p>";
        
        $stmt = $pdo->prepare("
            SELECT id, email, password, first_name, last_name, status 
            FROM users 
            WHERE email = ? AND status = 'active'
        ");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<p style='color: green;'>✓ User found: " . $user['first_name'] . " " . $user['last_name'] . "</p>";
            
            if (password_verify($password, $user['password'])) {
                echo "<p style='color: green;'>✓ Password verified</p>";
                
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                
                echo "<p style='color: green;'>✓ Session variables set</p>";
                echo "<p>Session user_id: " . $_SESSION['user_id'] . "</p>";
                echo "<p>Session user_email: " . $_SESSION['user_email'] . "</p>";
                echo "<p>Session user_name: " . $_SESSION['user_name'] . "</p>";
                
                // Test redirect
                echo "<h3>Testing Redirect Methods:</h3>";
                echo "<p>SITE_URL: " . SITE_URL . "</p>";
                
                // Method 1: JavaScript redirect
                echo "<p>Method 1: JavaScript redirect (in 3 seconds)</p>";
                echo "<script>
                    setTimeout(function() {
                        console.log('Redirecting to: " . SITE_URL . "');
                        window.location.href = '" . SITE_URL . "';
                    }, 3000);
                </script>";
                
                // Method 2: Manual links
                echo "<p>Method 2: Manual links</p>";
                echo "<p><a href='" . SITE_URL . "' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Home Page</a></p>";
                echo "<p><a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to index.php</a></p>";
                
            } else {
                echo "<p style='color: red;'>✗ Password verification failed</p>";
            }
        } else {
            echo "<p style='color: red;'>✗ User not found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
    }
}

// Show login form if not logged in
if (!isset($_SESSION['user_id'])) {
    echo "<h2>3. Quick Login Test</h2>";
    echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 5px;'>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label>Email:</label><br>";
    echo "<input type='email' name='email' value='<EMAIL>' style='width: 100%; padding: 8px;' required>";
    echo "</div>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label>Password:</label><br>";
    echo "<input type='password' name='password' value='user123' style='width: 100%; padding: 8px;' required>";
    echo "</div>";
    echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Login</button>";
    echo "</form>";
}

// Check if index.php is accessible
echo "<h2>4. Home Page Accessibility Test</h2>";
$index_path = __DIR__ . '/index.php';
if (file_exists($index_path)) {
    echo "<p style='color: green;'>✓ index.php file exists</p>";
    echo "<p>File path: " . $index_path . "</p>";
    
    // Test if index.php loads without errors
    ob_start();
    $error = '';
    try {
        include $index_path;
        $content = ob_get_contents();
        echo "<p style='color: green;'>✓ index.php loads without fatal errors</p>";
    } catch (Exception $e) {
        $error = $e->getMessage();
        echo "<p style='color: red;'>✗ index.php has errors: " . $error . "</p>";
    }
    ob_end_clean();
} else {
    echo "<p style='color: red;'>✗ index.php file not found</p>";
}

// Check SITE_URL configuration
echo "<h2>5. Configuration Check</h2>";
echo "<p>SITE_URL: " . SITE_URL . "</p>";
echo "<p>Current URL: " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] . "</p>";

// Test direct access to home page
echo "<h2>6. Direct Access Links</h2>";
echo "<p><a href='" . SITE_URL . "' target='_blank'>Test SITE_URL</a></p>";
echo "<p><a href='index.php' target='_blank'>Test index.php</a></p>";
echo "<p><a href='login.php' target='_blank'>Back to Login Page</a></p>";

echo "</div>";
?>
