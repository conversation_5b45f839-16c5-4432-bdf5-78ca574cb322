<?php
require_once 'config/config.php';

$page_title = 'My Orders';
$page_description = 'View and track your order history';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    // Store the current page for redirect after login
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    $_SESSION['error'] = 'Please login to view your orders.';

    // Use JavaScript redirect instead of PHP redirect to avoid header issues
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Redirecting to Login...</title>
        <meta http-equiv='refresh' content='0;url=" . SITE_URL . "/login.php'>
    </head>
    <body>
        <script>
            window.location.href = '" . SITE_URL . "/login.php';
        </script>
        <p>Redirecting to login page... <a href='" . SITE_URL . "/login.php'>Click here if not redirected automatically</a></p>
    </body>
    </html>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$where_conditions = ['o.user_id = ?'];
$params = [$user_id];

if (!empty($status_filter)) {
    $where_conditions[] = 'o.status = ?';
    $params[] = $status_filter;
}

if (!empty($search)) {
    $where_conditions[] = '(o.order_number LIKE ? OR p.name LIKE ?)';
    $params[] = '%' . $search . '%';
    $params[] = '%' . $search . '%';
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

try {
    $pdo = getDBConnection();
    
    // Get orders with item count
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as item_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        {$where_clause}
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
} catch (Exception $e) {
    $orders = [];
    $error_message = 'Error loading orders: ' . $e->getMessage();
    error_log("My Orders Error: " . $e->getMessage());
}

include 'includes/header.php';
?>

<!-- Error Display -->
<?php if (isset($error_message)): ?>
<div class="container mt-3">
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
<?php endif; ?>

<div class="container-lg my-5">
    <div class="row">
        <div class="col-12">

            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h2 mb-1 text-primary">
                        <i class="fas fa-shopping-bag me-2"></i>My Orders
                    </h1>
                    <p class="text-muted">Track and manage your order history</p>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary fs-6 mb-2"><?php echo count($orders); ?> Orders</span>
                    <br>
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>Shop More
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Filter by Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Orders</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                <option value="shipped" <?php echo $status_filter === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="search" class="form-label">Search Orders</label>
                            <input type="text" name="search" id="search" class="form-control" 
                                   placeholder="Search by order number or product name..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders List -->
            <?php if (empty($orders)): ?>
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                                <i class="fas fa-shopping-bag fa-3x text-muted"></i>
                            </div>
                        </div>
                        <h3 class="text-gray-900 mb-3">No Orders Found</h3>
                        <p class="text-muted mb-4 lead">
                            <?php if (!empty($status_filter) || !empty($search)): ?>
                                No orders match your current filters. Try adjusting your search criteria.
                            <?php else: ?>
                                You haven't placed any orders yet. Start shopping to see your orders here!
                            <?php endif; ?>
                        </p>

                        <?php if (!empty($status_filter) || !empty($search)): ?>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="<?php echo SITE_URL; ?>/my-orders.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-filter me-2"></i>Clear Filters
                                </a>
                                <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart me-2"></i>Continue Shopping
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                                </a>
                                <a href="<?php echo SITE_URL; ?>/index.php" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>Browse Categories
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($orders as $order): ?>
                        <?php
                        $status_colors = [
                            'pending' => 'warning',
                            'processing' => 'info',
                            'shipped' => 'primary',
                            'delivered' => 'success',
                            'cancelled' => 'danger'
                        ];
                        
                        $payment_colors = [
                            'pending' => 'warning',
                            'paid' => 'success',
                            'failed' => 'danger',
                            'refunded' => 'secondary'
                        ];
                        
                        $status_color = $status_colors[$order['status']] ?? 'secondary';
                        $payment_color = $payment_colors[$order['payment_status']] ?? 'secondary';
                        ?>
                        <div class="col-lg-6 mb-4">
                            <div class="card h-100 shadow-sm border-0 hover-card">
                                <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1 text-primary fw-bold">
                                            <i class="fas fa-receipt me-2"></i>
                                            <?php echo htmlspecialchars($order['order_number']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo formatDate($order['created_at']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-<?php echo $status_color; ?> mb-1 px-3 py-2">
                                            <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                                            <?php echo ucfirst($order['status']); ?>
                                        </span>
                                        <br>
                                        <span class="badge bg-<?php echo $payment_color; ?> px-3 py-1">
                                            <i class="fas fa-credit-card me-1"></i>
                                            <?php echo ucfirst($order['payment_status']); ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">Items</small>
                                            <div class="fw-bold"><?php echo $order['item_count']; ?> item(s)</div>
                                        </div>
                                        <div class="col-6 text-end">
                                            <small class="text-muted">Total</small>
                                            <div class="fw-bold text-success"><?php echo formatPrice($order['total_amount']); ?></div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">Products</small>
                                        <div class="small">
                                            <?php 
                                            $product_names = $order['product_names'] ?? 'No products';
                                            echo strlen($product_names) > 60 ? substr($product_names, 0, 60) . '...' : $product_names;
                                            ?>
                                        </div>
                                    </div>
                                    
                                    <?php if (!empty($order['payment_method'])): ?>
                                    <div class="mb-3">
                                        <small class="text-muted">Payment Method</small>
                                        <div class="small"><?php echo htmlspecialchars($order['payment_method']); ?></div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo timeAgo($order['created_at']); ?>
                                        </small>
                                        <div>
                                            <a href="order-confirmation.php?order=<?php echo $order['order_number']; ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>View Details
                                            </a>
                                            <?php if ($order['status'] === 'pending'): ?>
                                                <button class="btn btn-sm btn-outline-danger ms-1" 
                                                        onclick="cancelOrder('<?php echo $order['id']; ?>')">
                                                    <i class="fas fa-times me-1"></i>Cancel
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
        </div>
    </div>
</div>

<style>
.hover-card {
    transition: all 0.3s ease;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.text-gray-900 {
    color: #1a202c !important;
}

.order-status-pending { color: #f59e0b; }
.order-status-processing { color: #3b82f6; }
.order-status-shipped { color: #8b5cf6; }
.order-status-delivered { color: #10b981; }
.order-status-cancelled { color: #ef4444; }

.payment-status-pending { color: #f59e0b; }
.payment-status-paid { color: #10b981; }
.payment-status-failed { color: #ef4444; }
.payment-status-refunded { color: #6b7280; }
</style>

<script>
function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Cancelling...';
        button.disabled = true;

        // Simulate API call (replace with actual implementation)
        setTimeout(() => {
            alert('Order cancellation feature will be implemented soon.');
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    }
}

// Auto-refresh order status every 30 seconds
setInterval(function() {
    // You can implement real-time status updates here
    console.log('Checking for order updates...');
}, 30000);

// Add loading states to filter form
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    if (filterForm) {
        filterForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Filtering...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
