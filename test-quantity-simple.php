<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Quantity Controls</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .qty-btn {
            border-radius: 0;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
            font-weight: bold;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qty-btn:hover {
            background-color: #f8f9fa;
            border-color: #adb5bd;
            transform: scale(1.05);
        }

        .qty-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        #quantity {
            border: 2px solid #dee2e6;
            border-left: 0;
            border-right: 0;
            font-size: 1.1rem;
            font-weight: bold;
            height: 40px;
        }

        .test-card {
            max-width: 500px;
            margin: 0 auto;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary">🧪 Simple Quantity Test</h1>
            <p class="lead">Testing the + and - buttons functionality</p>
        </div>

        <div class="card test-card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Quantity Controls Test</h5>
            </div>
            <div class="card-body p-4">
                <div class="mb-4">
                    <label class="form-label mb-3">Quantity:</label>
                    <div class="input-group" style="width: 140px; margin: 0 auto;">
                        <button class="btn btn-outline-secondary qty-btn" type="button" id="decrease-qty">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="form-control text-center" id="quantity" 
                               value="1" min="1" max="10" 
                               style="width: 60px; font-weight: bold;">
                        <button class="btn btn-outline-secondary qty-btn" type="button" id="increase-qty">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <small class="text-muted d-block mt-2">Max: 10 available</small>
                </div>

                <div class="text-center">
                    <button class="btn btn-primary btn-lg add-to-cart-btn" id="add-to-cart">
                        <i class="fas fa-cart-plus me-2"></i>Add 1 to Cart
                    </button>
                </div>

                <div class="mt-4">
                    <h6>Test Results:</h6>
                    <div id="test-results" class="alert alert-info">
                        <p class="mb-1"><strong>Current Value:</strong> <span id="current-value">1</span></p>
                        <p class="mb-1"><strong>Decrease Button:</strong> <span id="decrease-status">Enabled</span></p>
                        <p class="mb-0"><strong>Increase Button:</strong> <span id="increase-status">Enabled</span></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <div class="card test-card">
                <div class="card-body">
                    <h6 class="fw-bold mb-3">Instructions</h6>
                    <ul class="text-start small">
                        <li>Click the <strong>+</strong> button to increase quantity</li>
                        <li>Click the <strong>-</strong> button to decrease quantity</li>
                        <li>Try typing directly in the input field</li>
                        <li>Watch the button states change</li>
                        <li>Test the min (1) and max (10) limits</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple vanilla JavaScript for quantity controls
        document.addEventListener('DOMContentLoaded', function() {
            const quantityInput = document.getElementById('quantity');
            const increaseBtn = document.getElementById('increase-qty');
            const decreaseBtn = document.getElementById('decrease-qty');
            const addToCartBtn = document.getElementById('add-to-cart');
            
            console.log('Elements found:', {
                quantityInput: !!quantityInput,
                increaseBtn: !!increaseBtn,
                decreaseBtn: !!decreaseBtn,
                addToCartBtn: !!addToCartBtn
            });
            
            function updateDisplay() {
                const qty = parseInt(quantityInput.value) || 1;
                const max = parseInt(quantityInput.getAttribute('max'));
                const min = parseInt(quantityInput.getAttribute('min'));
                
                // Update button states
                decreaseBtn.disabled = qty <= min;
                increaseBtn.disabled = qty >= max;
                
                // Update add to cart button
                addToCartBtn.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Add ' + qty + ' to Cart';
                
                // Update test results
                document.getElementById('current-value').textContent = qty;
                document.getElementById('decrease-status').textContent = decreaseBtn.disabled ? 'Disabled' : 'Enabled';
                document.getElementById('increase-status').textContent = increaseBtn.disabled ? 'Disabled' : 'Enabled';
                
                console.log('Updated display:', { qty, min, max, decreaseDisabled: decreaseBtn.disabled, increaseDisabled: increaseBtn.disabled });
            }
            
            // Increase quantity
            increaseBtn.addEventListener('click', function() {
                console.log('Increase button clicked');
                const max = parseInt(quantityInput.getAttribute('max'));
                const current = parseInt(quantityInput.value) || 1;
                
                if (current < max) {
                    quantityInput.value = current + 1;
                    // Visual feedback
                    this.classList.add('btn-success');
                    this.classList.remove('btn-outline-secondary');
                    setTimeout(() => {
                        this.classList.remove('btn-success');
                        this.classList.add('btn-outline-secondary');
                    }, 200);
                } else {
                    // Max reached feedback
                    this.classList.add('btn-warning');
                    this.classList.remove('btn-outline-secondary');
                    setTimeout(() => {
                        this.classList.remove('btn-warning');
                        this.classList.add('btn-outline-secondary');
                    }, 500);
                    alert('Maximum quantity reached!');
                }
                updateDisplay();
            });
            
            // Decrease quantity
            decreaseBtn.addEventListener('click', function() {
                console.log('Decrease button clicked');
                const current = parseInt(quantityInput.value) || 1;
                
                if (current > 1) {
                    quantityInput.value = current - 1;
                    // Visual feedback
                    this.classList.add('btn-danger');
                    this.classList.remove('btn-outline-secondary');
                    setTimeout(() => {
                        this.classList.remove('btn-danger');
                        this.classList.add('btn-outline-secondary');
                    }, 200);
                } else {
                    // Min reached feedback
                    this.classList.add('btn-warning');
                    this.classList.remove('btn-outline-secondary');
                    setTimeout(() => {
                        this.classList.remove('btn-warning');
                        this.classList.add('btn-outline-secondary');
                    }, 500);
                    alert('Minimum quantity is 1!');
                }
                updateDisplay();
            });
            
            // Handle manual input
            quantityInput.addEventListener('input', function() {
                console.log('Input changed:', this.value);
                const max = parseInt(this.getAttribute('max'));
                const min = parseInt(this.getAttribute('min'));
                let current = parseInt(this.value) || 1;
                
                if (current > max) {
                    this.value = max;
                    alert('Maximum quantity is ' + max);
                } else if (current < min) {
                    this.value = min;
                    alert('Minimum quantity is ' + min);
                }
                updateDisplay();
            });
            
            // Add to cart test
            addToCartBtn.addEventListener('click', function() {
                const qty = parseInt(quantityInput.value) || 1;
                alert('Would add ' + qty + ' items to cart!');
            });
            
            // Initialize display
            updateDisplay();
            
            console.log('Quantity controls initialized successfully');
        });
    </script>
</body>
</html>
