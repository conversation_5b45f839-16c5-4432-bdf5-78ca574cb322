<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Products Table - Add Missing Columns</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔧 Fix Products Table - Add Missing Columns</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='step'>";
    echo "<h2>Step 1: Check Current Products Table Structure</h2>";
    
    // Get current table structure
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll();
    
    echo "<h5>Current Columns:</h5>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-striped'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "<tr>";
        echo "<td><code>" . $column['Field'] . "</code></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Check Missing Columns</h2>";
    
    $required_columns = [
        'weight' => 'DECIMAL(8,2) DEFAULT 0',
        'dimensions' => 'VARCHAR(100) DEFAULT NULL'
    ];
    
    $missing_columns = [];
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            $missing_columns[$column] = $definition;
            echo "<p class='warning'>⚠️ Missing column: <code>$column</code></p>";
        } else {
            echo "<p class='success'>✅ Column exists: <code>$column</code></p>";
        }
    }
    
    if (empty($missing_columns)) {
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ All Required Columns Exist!</h5>";
        echo "<p>The products table already has all the required columns. The error might be something else.</p>";
        echo "</div>";
    }
    echo "</div>";
    
    if (!empty($missing_columns)) {
        echo "<div class='step'>";
        echo "<h2>Step 3: Add Missing Columns</h2>";
        
        foreach ($missing_columns as $column => $definition) {
            try {
                $sql = "ALTER TABLE products ADD COLUMN $column $definition";
                echo "<p class='info'>Executing: <code>$sql</code></p>";
                $pdo->exec($sql);
                echo "<p class='success'>✅ Added column: <code>$column</code></p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error adding column <code>$column</code>: " . $e->getMessage() . "</p>";
            }
        }
        echo "</div>";
    }
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Verify Table Structure After Fix</h2>";
    
    // Get updated table structure
    $stmt = $pdo->query("DESCRIBE products");
    $updated_columns = $stmt->fetchAll();
    
    echo "<h5>Updated Table Structure:</h5>";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-sm table-striped'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    
    foreach ($updated_columns as $column) {
        $is_new = in_array($column['Field'], array_keys($missing_columns));
        $row_class = $is_new ? 'table-success' : '';
        
        echo "<tr class='$row_class'>";
        echo "<td><code>" . $column['Field'] . "</code>";
        if ($is_new) echo " <span class='badge bg-success'>NEW</span>";
        echo "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Test Product Update Query</h2>";
    
    // Test the exact query from product-edit.php
    try {
        $test_stmt = $pdo->prepare("
            UPDATE products 
            SET name = ?, slug = ?, description = ?, short_description = ?, price = ?, sale_price = ?, 
                sku = ?, stock_quantity = ?, category_id = ?, image = ?, status = ?, featured = ?, 
                weight = ?, dimensions = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        echo "<p class='success'>✅ Product update query prepared successfully!</p>";
        echo "<p class='info'>The query that was failing should now work properly.</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Query still has issues: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
    
    echo "<div class='alert alert-success text-center'>";
    echo "<h3>🎉 Products Table Fixed!</h3>";
    echo "<p>The missing columns have been added. You should now be able to edit products without errors.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Database Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-5'>";
echo "<h4 class='mb-4'>Test Product Editing Now!</h4>";
echo "<div class='d-flex justify-content-center gap-3 flex-wrap'>";
echo "<a href='admin/products.php' class='btn btn-primary btn-lg'><i class='fas fa-box me-2'></i>Admin Products</a>";
echo "<a href='admin/product-edit.php?id=1' class='btn btn-success btn-lg'><i class='fas fa-edit me-2'></i>Test Edit Product</a>";
echo "<a href='admin/index.php' class='btn btn-info btn-lg'><i class='fas fa-tachometer-alt me-2'></i>Admin Dashboard</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
