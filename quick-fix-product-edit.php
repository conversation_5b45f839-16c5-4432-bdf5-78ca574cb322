<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Quick Fix - Product Edit Error</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>⚡ Quick Fix - Product Edit Error</h1>";

$fixed = false;
$error_message = '';

try {
    $pdo = getDBConnection();
    
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>Fixing Product Edit Error</h5>";
    echo "<p>The error occurs because the products table is missing 'weight' and 'dimensions' columns.</p>";
    echo "</div>";
    
    // Check if columns exist
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $has_weight = in_array('weight', $columns);
    $has_dimensions = in_array('dimensions', $columns);
    
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5 class='mb-0'>Column Status Check</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<ul class='list-unstyled mb-0'>";
    echo "<li class='" . ($has_weight ? 'text-success' : 'text-danger') . "'>";
    echo "<i class='fas fa-" . ($has_weight ? 'check' : 'times') . " me-2'></i>";
    echo "Weight column: " . ($has_weight ? 'EXISTS' : 'MISSING');
    echo "</li>";
    echo "<li class='" . ($has_dimensions ? 'text-success' : 'text-danger') . "'>";
    echo "<i class='fas fa-" . ($has_dimensions ? 'check' : 'times') . " me-2'></i>";
    echo "Dimensions column: " . ($has_dimensions ? 'EXISTS' : 'MISSING');
    echo "</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    // Add missing columns
    if (!$has_weight || !$has_dimensions) {
        echo "<div class='alert alert-warning'>";
        echo "<h6><i class='fas fa-wrench me-2'></i>Adding Missing Columns</h6>";
        
        if (!$has_weight) {
            try {
                $pdo->exec("ALTER TABLE products ADD COLUMN weight DECIMAL(8,2) DEFAULT 0");
                echo "<p class='text-success mb-1'>✅ Added 'weight' column</p>";
            } catch (Exception $e) {
                echo "<p class='text-danger mb-1'>❌ Error adding 'weight' column: " . $e->getMessage() . "</p>";
            }
        }
        
        if (!$has_dimensions) {
            try {
                $pdo->exec("ALTER TABLE products ADD COLUMN dimensions VARCHAR(100) DEFAULT NULL");
                echo "<p class='text-success mb-1'>✅ Added 'dimensions' column</p>";
            } catch (Exception $e) {
                echo "<p class='text-danger mb-1'>❌ Error adding 'dimensions' column: " . $e->getMessage() . "</p>";
            }
        }
        echo "</div>";
    }
    
    // Test the update query
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'>Testing Product Update Query</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    try {
        $test_stmt = $pdo->prepare("
            UPDATE products 
            SET name = ?, slug = ?, description = ?, short_description = ?, price = ?, sale_price = ?, 
                sku = ?, stock_quantity = ?, category_id = ?, image = ?, status = ?, featured = ?, 
                weight = ?, dimensions = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        
        echo "<p class='text-success mb-0'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "Product update query prepared successfully! The edit form should now work.";
        echo "</p>";
        $fixed = true;
        
    } catch (Exception $e) {
        echo "<p class='text-danger mb-0'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "Query still has issues: " . $e->getMessage();
        echo "</p>";
        $error_message = $e->getMessage();
    }
    echo "</div>";
    echo "</div>";
    
    if ($fixed) {
        echo "<div class='alert alert-success'>";
        echo "<h4><i class='fas fa-check-circle me-2'></i>Fix Complete!</h4>";
        echo "<p class='mb-3'>The product edit error has been fixed. You can now:</p>";
        echo "<ul class='mb-3'>";
        echo "<li>Edit products in the admin panel without errors</li>";
        echo "<li>Update product weight and dimensions</li>";
        echo "<li>Save product changes successfully</li>";
        echo "</ul>";
        echo "<div class='d-flex gap-3'>";
        echo "<a href='admin/products.php' class='btn btn-primary'>";
        echo "<i class='fas fa-box me-2'></i>Go to Products";
        echo "</a>";
        echo "<a href='admin/product-edit.php?id=1' class='btn btn-success'>";
        echo "<i class='fas fa-edit me-2'></i>Test Edit Product";
        echo "</a>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>Fix Failed</h4>";
        echo "<p>There are still issues with the product edit functionality:</p>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($error_message) . "</p>";
        echo "<p>Please check the database structure and try again.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>Database Error</h4>";
    echo "<p>Error connecting to database: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h5 class='mb-0'>What Was Fixed</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6 class='fw-bold mb-3'>The Problem</h6>";
echo "<ul class='small'>";
echo "<li>Product edit form was trying to update 'weight' and 'dimensions' columns</li>";
echo "<li>These columns didn't exist in the products table</li>";
echo "<li>SQL error: 'Unknown column weight in field list'</li>";
echo "<li>Product updates were failing</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6 class='fw-bold mb-3'>The Solution</h6>";
echo "<ul class='small'>";
echo "<li>Added missing 'weight' column (DECIMAL)</li>";
echo "<li>Added missing 'dimensions' column (VARCHAR)</li>";
echo "<li>Updated product-edit.php to handle missing columns gracefully</li>";
echo "<li>Product updates now work properly</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-4'>";
echo "<h5 class='mb-3'>Quick Actions</h5>";
echo "<div class='d-flex justify-content-center gap-3 flex-wrap'>";
echo "<a href='admin/index.php' class='btn btn-primary'>";
echo "<i class='fas fa-tachometer-alt me-2'></i>Admin Dashboard";
echo "</a>";
echo "<a href='admin/products.php' class='btn btn-success'>";
echo "<i class='fas fa-box me-2'></i>Manage Products";
echo "</a>";
echo "<a href='fix-products-table.php' class='btn btn-info'>";
echo "<i class='fas fa-database me-2'></i>Detailed Fix";
echo "</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
