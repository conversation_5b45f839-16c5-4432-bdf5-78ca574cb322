<?php
require_once 'config/config.php';

$page_title = 'Complete Order Workflow Test';

// Handle different workflow steps
$step = $_GET['step'] ?? 'start';
$action = $_POST['action'] ?? '';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>$page_title</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
    <style>
        .workflow-step { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .step-active { border-color: #007bff; background: #e7f3ff; }
        .step-completed { border-color: #28a745; background: #d4edda; }
        .order-number { font-size: 1.2rem; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>";

echo "<div class='container my-5'>";
echo "<h1 class='text-center mb-4'><i class='fas fa-shopping-cart me-2'></i>Complete Order Workflow</h1>";

// Progress indicator
$steps = ['start', 'product', 'cart', 'checkout', 'review', 'success'];
$current_step_index = array_search($step, $steps);

echo "<div class='row mb-4'>";
echo "<div class='col-12'>";
echo "<div class='d-flex justify-content-between'>";
foreach ($steps as $index => $step_name) {
    $class = '';
    if ($index < $current_step_index) $class = 'text-success';
    elseif ($index == $current_step_index) $class = 'text-primary fw-bold';
    else $class = 'text-muted';
    
    echo "<div class='text-center $class'>";
    echo "<div class='mb-1'><i class='fas fa-circle'></i></div>";
    echo "<small>" . ucfirst($step_name) . "</small>";
    echo "</div>";
}
echo "</div>";
echo "</div>";
echo "</div>";

try {
    $pdo = getDBConnection();
    
    switch ($step) {
        case 'start':
            echo "<div class='workflow-step step-active'>";
            echo "<h3><i class='fas fa-play text-primary me-2'></i>Step 1: Start Order Process</h3>";
            echo "<p>This workflow will demonstrate the complete order process from product selection to order confirmation.</p>";
            echo "<div class='d-grid gap-2 col-md-6 mx-auto'>";
            echo "<a href='?step=product' class='btn btn-primary btn-lg'>";
            echo "<i class='fas fa-arrow-right me-2'></i>Start with Product Selection";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            break;
            
        case 'product':
            // Get a sample product
            $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
            $product = $stmt->fetch();
            
            if (!$product) {
                echo "<div class='alert alert-warning'>No products found. Please add some products first.</div>";
                break;
            }
            
            echo "<div class='workflow-step step-active'>";
            echo "<h3><i class='fas fa-box text-primary me-2'></i>Step 2: Product Selection</h3>";
            echo "<div class='row'>";
            echo "<div class='col-md-4'>";
            echo "<img src='uploads/" . ($product['image'] ?: 'default-product.jpg') . "' class='img-fluid rounded' alt='" . htmlspecialchars($product['name']) . "'>";
            echo "</div>";
            echo "<div class='col-md-8'>";
            echo "<h4>" . htmlspecialchars($product['name']) . "</h4>";
            echo "<p class='text-muted'>" . htmlspecialchars($product['short_description'] ?? '') . "</p>";
            echo "<p class='h5 text-primary'>" . formatPrice($product['price']) . "</p>";
            echo "<form method='POST'>";
            echo "<input type='hidden' name='action' value='add_to_cart'>";
            echo "<input type='hidden' name='product_id' value='" . $product['id'] . "'>";
            echo "<div class='row mb-3'>";
            echo "<div class='col-auto'>";
            echo "<label for='quantity' class='form-label'>Quantity:</label>";
            echo "<input type='number' class='form-control' id='quantity' name='quantity' value='1' min='1' max='" . $product['stock_quantity'] . "'>";
            echo "</div>";
            echo "</div>";
            echo "<button type='submit' class='btn btn-success btn-lg'>";
            echo "<i class='fas fa-cart-plus me-2'></i>Add to Cart";
            echo "</button>";
            echo "</form>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // Handle add to cart
            if ($action === 'add_to_cart') {
                $product_id = (int)$_POST['product_id'];
                $quantity = (int)$_POST['quantity'];
                $session_id = session_id();
                
                // Add to cart
                $stmt = $pdo->prepare("
                    INSERT INTO cart (session_id, product_id, quantity, created_at) 
                    VALUES (?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE quantity = quantity + VALUES(quantity)
                ");
                $stmt->execute([$session_id, $product_id, $quantity]);
                
                echo "<script>window.location.href = '?step=cart';</script>";
            }
            break;
            
        case 'cart':
            // Get cart items
            $session_id = session_id();
            $stmt = $pdo->prepare("
                SELECT c.*, p.name, p.price, p.sale_price, p.image 
                FROM cart c 
                JOIN products p ON c.product_id = p.id 
                WHERE c.session_id = ? AND c.user_id IS NULL
            ");
            $stmt->execute([$session_id]);
            $cart_items = $stmt->fetchAll();
            
            if (empty($cart_items)) {
                echo "<div class='alert alert-warning'>Cart is empty. <a href='?step=product'>Add some products</a>.</div>";
                break;
            }
            
            $subtotal = 0;
            foreach ($cart_items as $item) {
                $price = $item['sale_price'] ?: $item['price'];
                $subtotal += $price * $item['quantity'];
            }
            
            echo "<div class='workflow-step step-active'>";
            echo "<h3><i class='fas fa-shopping-cart text-primary me-2'></i>Step 3: Shopping Cart</h3>";
            echo "<div class='table-responsive'>";
            echo "<table class='table'>";
            echo "<thead><tr><th>Product</th><th>Price</th><th>Quantity</th><th>Total</th></tr></thead>";
            echo "<tbody>";
            foreach ($cart_items as $item) {
                $price = $item['sale_price'] ?: $item['price'];
                $total = $price * $item['quantity'];
                echo "<tr>";
                echo "<td>" . htmlspecialchars($item['name']) . "</td>";
                echo "<td>" . formatPrice($price) . "</td>";
                echo "<td>" . $item['quantity'] . "</td>";
                echo "<td>" . formatPrice($total) . "</td>";
                echo "</tr>";
            }
            echo "</tbody>";
            echo "</table>";
            echo "</div>";
            echo "<div class='text-end'>";
            echo "<h5>Subtotal: " . formatPrice($subtotal) . "</h5>";
            echo "</div>";
            echo "<div class='d-grid gap-2 col-md-6 mx-auto'>";
            echo "<a href='?step=checkout' class='btn btn-primary btn-lg'>";
            echo "<i class='fas fa-arrow-right me-2'></i>Proceed to Checkout";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            break;
            
        case 'checkout':
            echo "<div class='workflow-step step-active'>";
            echo "<h3><i class='fas fa-user text-primary me-2'></i>Step 4: Checkout Information</h3>";
            echo "<form method='POST'>";
            echo "<input type='hidden' name='action' value='process_checkout'>";
            echo "<div class='row'>";
            echo "<div class='col-md-6'>";
            echo "<h5>Customer Information</h5>";
            echo "<div class='mb-3'>";
            echo "<label for='first_name' class='form-label'>First Name *</label>";
            echo "<input type='text' class='form-control' id='first_name' name='first_name' value='John' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='last_name' class='form-label'>Last Name *</label>";
            echo "<input type='text' class='form-control' id='last_name' name='last_name' value='Doe' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='email' class='form-label'>Email *</label>";
            echo "<input type='email' class='form-control' id='email' name='email' value='<EMAIL>' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='phone' class='form-label'>Phone *</label>";
            echo "<input type='tel' class='form-control' id='phone' name='phone' value='+62123456789' required>";
            echo "</div>";
            echo "</div>";
            echo "<div class='col-md-6'>";
            echo "<h5>Shipping Address</h5>";
            echo "<div class='mb-3'>";
            echo "<label for='address_line_1' class='form-label'>Address Line 1 *</label>";
            echo "<input type='text' class='form-control' id='address_line_1' name='address_line_1' value='Jl. Sudirman No. 123' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='city' class='form-label'>City *</label>";
            echo "<input type='text' class='form-control' id='city' name='city' value='Jakarta' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='state' class='form-label'>State *</label>";
            echo "<input type='text' class='form-control' id='state' name='state' value='DKI Jakarta' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='postal_code' class='form-label'>Postal Code *</label>";
            echo "<input type='text' class='form-control' id='postal_code' name='postal_code' value='12345' required>";
            echo "</div>";
            echo "<div class='mb-3'>";
            echo "<label for='payment_method' class='form-label'>Payment Method *</label>";
            echo "<select class='form-select' id='payment_method' name='payment_method' required>";
            echo "<option value='bank_transfer'>Bank Transfer</option>";
            echo "<option value='credit_card'>Credit Card</option>";
            echo "<option value='e_wallet'>E-Wallet</option>";
            echo "</select>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "<div class='d-grid gap-2 col-md-6 mx-auto'>";
            echo "<button type='submit' class='btn btn-primary btn-lg'>";
            echo "<i class='fas fa-arrow-right me-2'></i>Review Order";
            echo "</button>";
            echo "</div>";
            echo "</form>";
            echo "</div>";
            
            // Handle checkout processing
            if ($action === 'process_checkout') {
                $_SESSION['checkout_data'] = [
                    'first_name' => $_POST['first_name'],
                    'last_name' => $_POST['last_name'],
                    'email' => $_POST['email'],
                    'phone' => $_POST['phone'],
                    'address_line_1' => $_POST['address_line_1'],
                    'city' => $_POST['city'],
                    'state' => $_POST['state'],
                    'postal_code' => $_POST['postal_code'],
                    'country' => 'Indonesia',
                    'payment_method' => $_POST['payment_method'],
                    'notes' => '',
                    'discount_amount' => 0,
                    'coupon_id' => null
                ];
                echo "<script>window.location.href = '?step=review';</script>";
            }
            break;
            
        case 'review':
            if (!isset($_SESSION['checkout_data'])) {
                echo "<div class='alert alert-warning'>Please complete checkout first. <a href='?step=checkout'>Go to checkout</a>.</div>";
                break;
            }
            
            // Get cart items for review
            $session_id = session_id();
            $stmt = $pdo->prepare("
                SELECT c.*, p.name, p.price, p.sale_price, p.image 
                FROM cart c 
                JOIN products p ON c.product_id = p.id 
                WHERE c.session_id = ? AND c.user_id IS NULL
            ");
            $stmt->execute([$session_id]);
            $cart_items = $stmt->fetchAll();
            
            $subtotal = 0;
            foreach ($cart_items as $item) {
                $price = $item['sale_price'] ?: $item['price'];
                $subtotal += $price * $item['quantity'];
            }
            
            $shipping_fee = $subtotal >= 100000 ? 0 : 15000;
            $tax_amount = $subtotal * 0.10;
            $total_amount = $subtotal + $shipping_fee + $tax_amount;
            
            echo "<div class='workflow-step step-active'>";
            echo "<h3><i class='fas fa-clipboard-check text-primary me-2'></i>Step 5: Order Review</h3>";
            echo "<div class='row'>";
            echo "<div class='col-md-8'>";
            echo "<h5>Order Items</h5>";
            foreach ($cart_items as $item) {
                $price = $item['sale_price'] ?: $item['price'];
                $total = $price * $item['quantity'];
                echo "<div class='d-flex justify-content-between border-bottom py-2'>";
                echo "<div>" . htmlspecialchars($item['name']) . " (x" . $item['quantity'] . ")</div>";
                echo "<div>" . formatPrice($total) . "</div>";
                echo "</div>";
            }
            echo "</div>";
            echo "<div class='col-md-4'>";
            echo "<div class='card'>";
            echo "<div class='card-body'>";
            echo "<h5>Order Summary</h5>";
            echo "<div class='d-flex justify-content-between'><span>Subtotal:</span><span>" . formatPrice($subtotal) . "</span></div>";
            echo "<div class='d-flex justify-content-between'><span>Shipping:</span><span>" . formatPrice($shipping_fee) . "</span></div>";
            echo "<div class='d-flex justify-content-between'><span>Tax:</span><span>" . formatPrice($tax_amount) . "</span></div>";
            echo "<hr>";
            echo "<div class='d-flex justify-content-between'><strong>Total:</strong><strong>" . formatPrice($total_amount) . "</strong></div>";
            echo "<form method='POST' class='mt-3'>";
            echo "<input type='hidden' name='action' value='place_order'>";
            echo "<div class='d-grid'>";
            echo "<button type='submit' class='btn btn-success btn-lg'>";
            echo "<i class='fas fa-check me-2'></i>Confirm & Place Order";
            echo "</button>";
            echo "</div>";
            echo "</form>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // Handle order placement
            if ($action === 'place_order') {
                $pdo->beginTransaction();
                
                try {
                    // Generate sequential order number
                    $stmt = $pdo->query("SELECT MAX(CAST(SUBSTRING(order_number, 10) AS UNSIGNED)) as max_num FROM orders WHERE order_number LIKE 'TWN-" . date('Y') . "-%'");
                    $result = $stmt->fetch();
                    $next_number = ($result['max_num'] ?? 0) + 1;
                    $order_number = 'TWN-' . date('Y') . '-' . str_pad($next_number, 4, '0', STR_PAD_LEFT);
                    
                    $checkout_data = $_SESSION['checkout_data'];
                    
                    // Create shipping address
                    $shipping_address = "{$checkout_data['first_name']} {$checkout_data['last_name']}\n{$checkout_data['address_line_1']}\n{$checkout_data['city']}, {$checkout_data['state']} {$checkout_data['postal_code']}\nIndonesia\nPhone: {$checkout_data['phone']}";
                    
                    // Customer info
                    $customer_info = json_encode([
                        'first_name' => $checkout_data['first_name'],
                        'last_name' => $checkout_data['last_name'],
                        'email' => $checkout_data['email'],
                        'phone' => $checkout_data['phone']
                    ]);
                    
                    // Insert order
                    $stmt = $pdo->prepare("
                        INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount,
                                          tax_amount, discount_amount, payment_method, payment_status,
                                          shipping_address, notes, customer_info, created_at)
                        VALUES (?, ?, 'pending', ?, ?, ?, ?, ?, 'pending', ?, ?, ?, NOW())
                    ");
                    
                    $stmt->execute([
                        $order_number, null, $total_amount, $shipping_fee, $tax_amount, 0,
                        $checkout_data['payment_method'], $shipping_address, '', $customer_info
                    ]);
                    
                    $order_id = $pdo->lastInsertId();
                    
                    // Insert order items
                    foreach ($cart_items as $item) {
                        $price = $item['sale_price'] ?: $item['price'];
                        $total = $price * $item['quantity'];
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO order_items (order_id, product_id, quantity, price, total)
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$order_id, $item['product_id'], $item['quantity'], $price, $total]);
                    }
                    
                    // Clear cart
                    $stmt = $pdo->prepare("DELETE FROM cart WHERE session_id = ? AND user_id IS NULL");
                    $stmt->execute([$session_id]);
                    
                    $pdo->commit();
                    
                    // Store order data for success page
                    $_SESSION['last_order'] = [
                        'order_id' => $order_id,
                        'order_number' => $order_number,
                        'total_amount' => $total_amount,
                        'customer_name' => $checkout_data['first_name'] . ' ' . $checkout_data['last_name']
                    ];
                    
                    echo "<script>window.location.href = '?step=success';</script>";
                    
                } catch (Exception $e) {
                    $pdo->rollBack();
                    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                }
            }
            break;
            
        case 'success':
            $order_data = $_SESSION['last_order'] ?? null;
            
            if (!$order_data) {
                echo "<div class='alert alert-warning'>No order data found.</div>";
                break;
            }
            
            echo "<div class='workflow-step step-completed'>";
            echo "<div class='text-center'>";
            echo "<div class='mb-4'>";
            echo "<i class='fas fa-check-circle text-success' style='font-size: 4rem;'></i>";
            echo "</div>";
            echo "<h2 class='text-success mb-3'>Order Placed Successfully!</h2>";
            echo "<div class='order-number mb-3'>Order Number: " . $order_data['order_number'] . "</div>";
            echo "<p class='lead'>Thank you " . htmlspecialchars($order_data['customer_name']) . " for your order!</p>";
            echo "<p>Total Amount: <strong>" . formatPrice($order_data['total_amount']) . "</strong></p>";
            echo "<p class='text-muted'>Your order is being processed and you will receive a confirmation email shortly.</p>";
            echo "<div class='mt-4'>";
            echo "<a href='order-confirmation.php?order=" . $order_data['order_number'] . "' class='btn btn-primary me-3'>";
            echo "<i class='fas fa-receipt me-2'></i>View Order Details";
            echo "</a>";
            echo "<a href='?step=start' class='btn btn-outline-primary'>";
            echo "<i class='fas fa-redo me-2'></i>Start New Order";
            echo "</a>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
            
            // Clear session data
            unset($_SESSION['last_order'], $_SESSION['checkout_data']);
            break;
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
}

echo "</div>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "</body></html>";
?>
