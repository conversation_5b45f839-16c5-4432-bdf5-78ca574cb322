<?php
require_once 'config/config.php';

echo "<h1>My Orders Debug Test</h1>";

// Check if user is logged in
echo "<h2>1. User Login Status</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✓ User is logged in</p>";
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Email: " . ($_SESSION['user_email'] ?? 'Not set') . "</p>";
    echo "<p>User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "</p>";
} else {
    echo "<p style='color: red;'>✗ User is NOT logged in</p>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login First</a></p>";
    echo "<p>You need to login to view orders. The my-orders.php page redirects to login if not logged in.</p>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Check database connection
echo "<h2>2. Database Connection</h2>";
try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Check if orders table exists
echo "<h2>3. Orders Table Check</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Orders table exists</p>";
    } else {
        echo "<p style='color: red;'>✗ Orders table does not exist</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error checking orders table: " . $e->getMessage() . "</p>";
    exit;
}

// Check user's orders
echo "<h2>4. User Orders Check</h2>";
try {
    // Get user orders
    $stmt = $pdo->prepare("
        SELECT o.*,
               COUNT(oi.id) as item_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.user_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $orders = $stmt->fetchAll();
    
    echo "<p>Found <strong>" . count($orders) . "</strong> orders for user ID " . $user_id . "</p>";
    
    if (count($orders) > 0) {
        echo "<h3>Orders List:</h3>";
        echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Order #</th><th>Items</th><th>Total</th><th>Status</th><th>Payment</th><th>Date</th>";
        echo "</tr>";
        
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td>" . $order['item_count'] . " items</td>";
            echo "<td>Rp " . number_format($order['total_amount'], 0, ',', '.') . "</td>";
            echo "<td><span style='background: orange; color: white; padding: 2px 8px; border-radius: 3px;'>" . ucfirst($order['status']) . "</span></td>";
            echo "<td><span style='background: blue; color: white; padding: 2px 8px; border-radius: 3px;'>" . ucfirst($order['payment_status']) . "</span></td>";
            echo "<td>" . date('d M Y H:i', strtotime($order['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠ No orders found for this user</p>";
        echo "<p>This means:</p>";
        echo "<ul>";
        echo "<li>The user hasn't placed any orders yet, OR</li>";
        echo "<li>The orders are associated with a different user ID, OR</li>";
        echo "<li>The orders exist but the JOIN query isn't working properly</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error getting user orders: " . $e->getMessage() . "</p>";
}

// Check all orders in database
echo "<h2>5. All Orders in Database</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM orders");
    $total_orders = $stmt->fetch()['total'];
    echo "<p>Total orders in database: <strong>" . $total_orders . "</strong></p>";
    
    if ($total_orders > 0) {
        $stmt = $pdo->query("
            SELECT o.order_number, o.user_id, o.status, o.total_amount, o.created_at,
                   u.email, u.first_name, u.last_name
            FROM orders o 
            LEFT JOIN users u ON o.user_id = u.id 
            ORDER BY o.created_at DESC 
            LIMIT 5
        ");
        $all_orders = $stmt->fetchAll();
        
        echo "<h3>Recent Orders (All Users):</h3>";
        echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Order #</th><th>User ID</th><th>Customer</th><th>Total</th><th>Status</th><th>Date</th>";
        echo "</tr>";
        
        foreach ($all_orders as $order) {
            $highlight = ($order['user_id'] == $user_id) ? "style='background: #ffffcc;'" : "";
            echo "<tr $highlight>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td>" . $order['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars(($order['first_name'] ?? '') . ' ' . ($order['last_name'] ?? '')) . "<br><small>" . htmlspecialchars($order['email'] ?? '') . "</small></td>";
            echo "<td>Rp " . number_format($order['total_amount'], 0, ',', '.') . "</td>";
            echo "<td>" . ucfirst($order['status']) . "</td>";
            echo "<td>" . date('d M Y H:i', strtotime($order['created_at'])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><small>Yellow highlighted rows are orders for the current user (ID: $user_id)</small></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error getting all orders: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Quick Actions</h2>";
echo "<p><a href='my-orders.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to My Orders Page</a></p>";
echo "<p><a href='order-review.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Place Test Order</a></p>";
echo "<p><a href='test-orders.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View All Orders (Admin)</a></p>";
?>
