<?php
// Quick Order Test - Simulate order placement
require_once 'config/config.php';

echo "<h2>Quick Order Test</h2>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5;'>";

// Check if we should create a test order
if (isset($_GET['create_test_order'])) {
    try {
        $pdo = getDBConnection();
        $pdo->beginTransaction();
        
        // Create test checkout data
        $test_checkout_data = [
            'first_name' => 'Test',
            'last_name' => 'Customer',
            'email' => '<EMAIL>',
            'phone' => '+***********',
            'address_line_1' => 'Test Address 123',
            'address_line_2' => '',
            'city' => 'Jakarta',
            'state' => 'DKI Jakarta',
            'postal_code' => '12345',
            'country' => 'Indonesia',
            'payment_method' => 'bank_transfer',
            'notes' => 'Test order'
        ];
        
        // Generate order number
        $order_number = 'TEST-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Create shipping address
        $shipping_address = "{$test_checkout_data['first_name']} {$test_checkout_data['last_name']}\n{$test_checkout_data['address_line_1']}";
        $shipping_address .= "\n{$test_checkout_data['city']}, {$test_checkout_data['state']} {$test_checkout_data['postal_code']}\n{$test_checkout_data['country']}";
        $shipping_address .= "\nPhone: {$test_checkout_data['phone']}";
        
        // Customer info
        $customer_info = json_encode([
            'first_name' => $test_checkout_data['first_name'],
            'last_name' => $test_checkout_data['last_name'],
            'email' => $test_checkout_data['email'],
            'phone' => $test_checkout_data['phone']
        ]);
        
        // Insert order
        $stmt = $pdo->prepare("
            INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount, 
                              tax_amount, discount_amount, payment_method, payment_status, 
                              shipping_address, notes, customer_info, created_at) 
            VALUES (?, ?, 'pending', ?, ?, ?, ?, ?, 'pending', ?, ?, ?, NOW())
        ");
        
        $user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
        $result = $stmt->execute([
            $order_number,
            $user_id,
            150000, // total amount
            15000,  // shipping
            13500,  // tax
            0,      // discount
            $test_checkout_data['payment_method'],
            $shipping_address,
            $test_checkout_data['notes'],
            $customer_info
        ]);
        
        if (!$result) {
            throw new Exception('Failed to insert order');
        }
        
        $order_id = $pdo->lastInsertId();
        
        // Get a test product for order items
        $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' LIMIT 1");
        $test_product = $stmt->fetch();
        
        if ($test_product) {
            // Insert test order item
            $stmt = $pdo->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, price, total) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $order_id,
                $test_product['id'],
                1,
                $test_product['price'],
                $test_product['price']
            ]);
        }
        
        $pdo->commit();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Test Order Created Successfully!</h3>";
        echo "<p><strong>Order Number:</strong> $order_number</p>";
        echo "<p><strong>Order ID:</strong> $order_id</p>";
        echo "<p><a href='order-confirmation.php?order=$order_number' style='color: #0d6efd;'>View Order Confirmation</a></p>";
        echo "<p><a href='admin/order-detail.php?id=$order_id' style='color: #0d6efd;'>View in Admin Panel</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Error Creating Test Order</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Show current status
try {
    $pdo = getDBConnection();
    
    echo "<h3>Current System Status</h3>";
    
    // Check session
    echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
    echo "<p><strong>User ID:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Guest') . "</p>";
    
    // Check checkout data
    if (isset($_SESSION['checkout_data'])) {
        echo "<p style='color: green;'>✅ Checkout data exists in session</p>";
    } else {
        echo "<p style='color: orange;'>⚠ No checkout data in session</p>";
    }
    
    // Check cart
    if (isset($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cart WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    } else {
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM cart WHERE session_id = ? AND user_id IS NULL");
        $stmt->execute([session_id()]);
    }
    $cart_count = $stmt->fetch()['count'];
    
    if ($cart_count > 0) {
        echo "<p style='color: green;'>✅ Cart has $cart_count items</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Cart is empty</p>";
    }
    
    // Check recent orders
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $recent_orders = $stmt->fetch()['count'];
    echo "<p><strong>Orders in last hour:</strong> $recent_orders</p>";
    
    // Check products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $active_products = $stmt->fetch()['count'];
    echo "<p><strong>Active products:</strong> $active_products</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error checking status: " . $e->getMessage() . "</p>";
}

echo "<h3>Test Actions</h3>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='?create_test_order=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Create Test Order</a>";
echo "<a href='test-order-processing.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Run Full Test</a>";
echo "<a href='checkout.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Checkout</a>";
echo "<a href='products.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Add to Cart</a>";
echo "</div>";

echo "<h3>Recent Orders</h3>";
try {
    $stmt = $pdo->query("SELECT * FROM orders ORDER BY created_at DESC LIMIT 10");
    $orders = $stmt->fetchAll();
    
    if (!empty($orders)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'><th>Order Number</th><th>Status</th><th>Total</th><th>Created</th><th>Actions</th></tr>";
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td>" . ucfirst($order['status']) . "</td>";
            echo "<td>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td>" . date('M j, Y H:i', strtotime($order['created_at'])) . "</td>";
            echo "<td>";
            echo "<a href='order-confirmation.php?order=" . $order['order_number'] . "' style='color: #007bff; margin-right: 10px;'>View</a>";
            echo "<a href='admin/order-detail.php?id=" . $order['id'] . "' style='color: #28a745;'>Admin</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: #6c757d;'>No orders found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error loading orders: " . $e->getMessage() . "</p>";
}

echo "<p style='margin-top: 30px;'><a href='index.php' style='color: #0d6efd;'>← Back to Home</a></p>";
echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 0;
    background-color: #f8f9fa;
}

table {
    font-size: 14px;
}

th, td {
    padding: 8px 12px;
    text-align: left;
}

th {
    background-color: #e9ecef;
}

a {
    text-decoration: none;
}

a:hover {
    opacity: 0.8;
}
</style>
