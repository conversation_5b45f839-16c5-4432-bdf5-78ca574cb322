<?php
require_once 'config/config.php';

$page_title = 'Test Product Buttons';
$page_description = 'Testing quantity controls and wishlist functionality';

// Get a sample product for testing
$product = null;
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' AND stock_quantity > 0 LIMIT 1");
    $product = $stmt->fetch();
} catch (Exception $e) {
    $error_message = 'Error loading product: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">🧪 Test Product Buttons</h1>
        <p class="lead text-gray-600">Testing quantity controls and wishlist functionality</p>
    </div>

    <?php if ($product): ?>
        <!-- Test Product Card -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-test-tube me-2"></i>
                            Testing: <?php echo htmlspecialchars($product['name']); ?>
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6">
                                <!-- Product Image -->
                                <?php if ($product['image']): ?>
                                    <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $product['image']; ?>" 
                                         class="img-fluid rounded mb-3" alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" style="height: 300px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <!-- Product Info -->
                                <h3 class="fw-bold mb-3"><?php echo htmlspecialchars($product['name']); ?></h3>
                                
                                <div class="mb-3">
                                    <span class="h4 fw-bold text-primary"><?php echo formatPrice($product['price']); ?></span>
                                </div>
                                
                                <div class="mb-4">
                                    <small class="text-muted">Stock: <?php echo $product['stock_quantity']; ?> available</small>
                                </div>

                                <!-- Quantity Controls (Same as product-detail.php) -->
                                <div class="add-to-cart mb-4">
                                    <div class="row g-3 align-items-center">
                                        <div class="col-auto">
                                            <label class="form-label mb-2">Quantity:</label>
                                            <div class="input-group" style="width: 140px;">
                                                <button class="btn btn-outline-secondary qty-btn" type="button" id="decrease-qty">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <input type="number" class="form-control text-center" id="quantity" 
                                                       value="1" min="1" max="<?php echo $product['stock_quantity']; ?>" 
                                                       style="width: 60px; font-weight: bold;">
                                                <button class="btn btn-outline-secondary qty-btn" type="button" id="increase-qty">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Max: <?php echo $product['stock_quantity']; ?> available</small>
                                        </div>
                                        <div class="col">
                                            <button class="btn btn-primary btn-lg add-to-cart-btn"
                                                    data-product-id="<?php echo $product['id']; ?>">
                                                <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                            </button>
                                            <button class="btn btn-success btn-lg ms-2 buy-now-btn"
                                                    data-product-id="<?php echo $product['id']; ?>">
                                                <i class="fas fa-bolt me-2"></i>Buy Now
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Wishlist Button -->
                                <div class="wishlist mb-4">
                                    <button class="btn btn-outline-danger btn-lg wishlist-btn" 
                                            data-product-id="<?php echo $product['id']; ?>"
                                            title="Add to Wishlist">
                                        <i class="fas fa-heart me-2"></i>Add to Wishlist
                                    </button>
                                    <small class="text-muted ms-3">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Save for later and get notified of price changes
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testing Instructions -->
        <div class="row justify-content-center mt-5">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">🧪 Testing Instructions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Quantity Controls</h6>
                                <ul class="small">
                                    <li>Click <strong>+</strong> button to increase quantity</li>
                                    <li>Click <strong>-</strong> button to decrease quantity</li>
                                    <li>Type directly in the input field</li>
                                    <li>Watch for visual feedback on buttons</li>
                                    <li>Test maximum and minimum limits</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Button Functions</h6>
                                <ul class="small">
                                    <li><strong>Add to Cart:</strong> Should show loading, then success</li>
                                    <li><strong>Buy Now:</strong> Should add to cart and redirect</li>
                                    <li><strong>Wishlist:</strong> Should toggle add/remove state</li>
                                    <li>Watch for toast notifications</li>
                                    <li>Check cart count updates</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="row justify-content-center mt-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">✅ Expected Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Quantity Buttons</h6>
                                <ul class="small">
                                    <li>Buttons change color when clicked</li>
                                    <li>Input field updates correctly</li>
                                    <li>Buttons disable at min/max limits</li>
                                    <li>Toast shows for limit warnings</li>
                                    <li>"Add to Cart" text updates with quantity</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-3">Action Buttons</h6>
                                <ul class="small">
                                    <li>Loading spinners appear during actions</li>
                                    <li>Success/error toast notifications</li>
                                    <li>Cart count updates in header</li>
                                    <li>Wishlist button toggles state</li>
                                    <li>Buy Now redirects to checkout</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <?php else: ?>
        <div class="alert alert-warning text-center">
            <h4>⚠️ No Products Available</h4>
            <p>No products found for testing. Please add some products first.</p>
            <a href="<?php echo SITE_URL; ?>/admin/products.php" class="btn btn-warning">Add Products</a>
        </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="text-center mt-5">
        <h4 class="mb-4">Quick Actions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Browse Products
            </a>
            <a href="<?php echo SITE_URL; ?>/cart.php" class="btn btn-success">
                <i class="fas fa-shopping-cart me-2"></i>View Cart
            </a>
            <a href="<?php echo SITE_URL; ?>/wishlist.php" class="btn btn-danger">
                <i class="fas fa-heart me-2"></i>View Wishlist
            </a>
        </div>
    </div>
</div>

<!-- Include the same JavaScript and CSS from product-detail.php -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Enhanced Quantity controls with visual feedback
    $('#increase-qty').click(function() {
        const qty = $('#quantity');
        const max = parseInt(qty.attr('max'));
        const current = parseInt(qty.val()) || 1;
        
        if (current < max) {
            qty.val(current + 1);
            // Add visual feedback
            $(this).addClass('btn-success').removeClass('btn-outline-secondary');
            setTimeout(() => {
                $(this).removeClass('btn-success').addClass('btn-outline-secondary');
            }, 200);
        } else {
            // Show max reached feedback
            $(this).addClass('btn-warning').removeClass('btn-outline-secondary');
            setTimeout(() => {
                $(this).removeClass('btn-warning').addClass('btn-outline-secondary');
            }, 500);
            showToast('Maximum quantity reached!', 'warning');
        }
        updateQuantityDisplay();
    });
    
    $('#decrease-qty').click(function() {
        const qty = $('#quantity');
        const current = parseInt(qty.val()) || 1;
        
        if (current > 1) {
            qty.val(current - 1);
            // Add visual feedback
            $(this).addClass('btn-danger').removeClass('btn-outline-secondary');
            setTimeout(() => {
                $(this).removeClass('btn-danger').addClass('btn-outline-secondary');
            }, 200);
        } else {
            // Show minimum reached feedback
            $(this).addClass('btn-warning').removeClass('btn-outline-secondary');
            setTimeout(() => {
                $(this).removeClass('btn-warning').addClass('btn-outline-secondary');
            }, 500);
            showToast('Minimum quantity is 1!', 'warning');
        }
        updateQuantityDisplay();
    });
    
    // Handle manual quantity input
    $('#quantity').on('input change', function() {
        const max = parseInt($(this).attr('max'));
        const min = parseInt($(this).attr('min'));
        let current = parseInt($(this).val()) || 1;
        
        if (current > max) {
            $(this).val(max);
            showToast('Maximum quantity is ' + max, 'warning');
        } else if (current < min) {
            $(this).val(min);
            showToast('Minimum quantity is ' + min, 'warning');
        }
        updateQuantityDisplay();
    });
    
    function updateQuantityDisplay() {
        const qty = parseInt($('#quantity').val()) || 1;
        const max = parseInt($('#quantity').attr('max'));
        
        // Update button states
        $('#decrease-qty').prop('disabled', qty <= 1);
        $('#increase-qty').prop('disabled', qty >= max);
        
        // Update add to cart button text
        $('.add-to-cart-btn').html('<i class="fas fa-cart-plus me-2"></i>Add ' + qty + ' to Cart');
    }
    
    // Add to cart with quantity
    $('.add-to-cart-btn').click(function() {
        const productId = $(this).data('product-id');
        const quantity = parseInt($('#quantity').val()) || 1;
        const button = $(this);
        
        // Show loading state
        const originalText = button.html();
        button.prop('disabled', true);
        button.html('<span class="spinner-border spinner-border-sm me-2"></span>Adding...');
        
        // Simulate AJAX request (replace with actual API call)
        setTimeout(() => {
            showToast('Added ' + quantity + ' item(s) to cart!', 'success');
            
            // Show success state
            button.html('<i class="fas fa-check me-2"></i>Added!');
            button.removeClass('btn-primary').addClass('btn-success');
            
            setTimeout(() => {
                button.html(originalText);
                button.removeClass('btn-success').addClass('btn-primary');
                button.prop('disabled', false);
            }, 2000);
        }, 1000);
    });
    
    // Buy Now functionality
    $('.buy-now-btn').click(function() {
        const quantity = parseInt($('#quantity').val()) || 1;
        const button = $(this);
        
        // Show loading state
        const originalText = button.html();
        button.prop('disabled', true);
        button.html('<span class="spinner-border spinner-border-sm me-2"></span>Processing...');
        
        // Simulate process
        setTimeout(() => {
            showToast('Product added to cart! Redirecting to checkout...', 'success');
            setTimeout(() => {
                alert('Would redirect to checkout page');
                button.html(originalText);
                button.prop('disabled', false);
            }, 1000);
        }, 1000);
    });
    
    // Initialize quantity display
    updateQuantityDisplay();
    
    // Toast notification function
    function showToast(message, type = 'info') {
        const toastContainer = $('#toast-container');
        if (toastContainer.length === 0) {
            $('body').append('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
        }
        
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'success' ? 'bg-success' : 
                       type === 'error' ? 'bg-danger' : 
                       type === 'warning' ? 'bg-warning' : 'bg-info';
        
        const toastHtml = `
            <div id="${toastId}" class="toast ${bgClass} text-white" role="alert">
                <div class="toast-header ${bgClass} text-white border-0">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        $('#toast-container').append(toastHtml);
        const toast = new bootstrap.Toast(document.getElementById(toastId));
        toast.show();
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            $('#' + toastId).remove();
        }, 5000);
    }
});
</script>

<style>
/* Enhanced quantity controls */
.qty-btn {
    border-radius: 0;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    font-weight: bold;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qty-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    transform: scale(1.05);
}

.qty-btn:active {
    transform: scale(0.95);
}

.qty-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#quantity {
    border: 2px solid #dee2e6;
    border-left: 0;
    border-right: 0;
    font-size: 1.1rem;
    font-weight: bold;
    height: 40px;
}

#quantity:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Wishlist button enhancement */
.wishlist-btn {
    transition: all 0.3s ease;
    border: 2px solid #dc3545;
}

.wishlist-btn:hover {
    background-color: #dc3545;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.wishlist-btn.in-wishlist {
    background-color: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* Add to cart button enhancement */
.add-to-cart-btn, .buy-now-btn {
    transition: all 0.3s ease;
    font-weight: bold;
}

.add-to-cart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.buy-now-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}
</style>

<?php include 'includes/footer.php'; ?>
