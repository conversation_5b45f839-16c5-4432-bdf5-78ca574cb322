<?php
require_once 'config/config.php';

echo "<h1>Authentication Status Test</h1>";

echo "<h2>PHP Session Status</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✓ User is logged in via PHP session</p>";
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Email: " . ($_SESSION['user_email'] ?? 'Not set') . "</p>";
    echo "<p>User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "</p>";
    echo "<p>Firebase UID: " . ($_SESSION['firebase_uid'] ?? 'Not set') . "</p>";
} else {
    echo "<p style='color: red;'>✗ User is NOT logged in via PHP session</p>";
}

echo "<h2>All Session Data</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Firebase Authentication Status</h2>";
echo "<p>Check the browser console for Firebase auth state.</p>";

echo "<div id='firebase-status'></div>";

echo "<h2>Quick Actions</h2>";
echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login</a></p>";
echo "<p><a href='my-orders.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to My Orders</a></p>";
echo "<p><a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Homepage</a></p>";
?>

<script type="module">
// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import {
    getAuth,
    onAuthStateChanged
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDz5t6mlBzXq7bjK3PGOGyEBo_WzjsHYME",
    authDomain: "tewuneed-marketplace.firebaseapp.com",
    databaseURL: "https://tewuneed-marketplace-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "tewuneed-marketplace",
    storageBucket: "tewuneed-marketplace.firebasestorage.app",
    messagingSenderId: "************",
    appId: "1:************:web:87b68aa3a5a5ebca395893",
    measurementId: "G-8WNLD8T7GY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Check Firebase auth state
onAuthStateChanged(auth, (user) => {
    const statusDiv = document.getElementById('firebase-status');
    if (user) {
        statusDiv.innerHTML = `
            <p style='color: green;'>✓ User is logged in via Firebase</p>
            <p>Firebase UID: ${user.uid}</p>
            <p>Email: ${user.email}</p>
            <p>Display Name: ${user.displayName || 'Not set'}</p>
        `;
        console.log('Firebase user:', user);
    } else {
        statusDiv.innerHTML = `<p style='color: red;'>✗ User is NOT logged in via Firebase</p>`;
        console.log('No Firebase user');
    }
});
</script>
