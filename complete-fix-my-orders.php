<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Complete My Orders Fix</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: Arial, sans-serif; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        h1, h2 { color: #333; }
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
    </style>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔧 Complete My Orders Fix</h1>";

try {
    $pdo = getDBConnection();
    echo "<div class='alert alert-success'>✅ Database connection successful</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 1: Check and Create Database Tables</h2>";
    
    // Check and create users table
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Users table missing, creating...</p>";
        $pdo->exec("
            CREATE TABLE users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(100) NOT NULL,
                last_name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                date_of_birth DATE,
                gender ENUM('male', 'female', 'other'),
                avatar VARCHAR(255),
                email_verified BOOLEAN DEFAULT FALSE,
                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_status (status)
            )
        ");
        echo "<p class='success'>✅ Users table created</p>";
    } else {
        echo "<p class='success'>✅ Users table exists</p>";
    }
    
    // Check and create orders table
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Orders table missing, creating...</p>";
        $pdo->exec("
            CREATE TABLE orders (
                id INT PRIMARY KEY AUTO_INCREMENT,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                user_id INT,
                status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                total_amount DECIMAL(10,2) NOT NULL,
                shipping_amount DECIMAL(10,2) DEFAULT 0,
                tax_amount DECIMAL(10,2) DEFAULT 0,
                discount_amount DECIMAL(10,2) DEFAULT 0,
                payment_method VARCHAR(50),
                payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
                shipping_address TEXT,
                billing_address TEXT,
                notes TEXT,
                shipped_at TIMESTAMP NULL,
                delivered_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_user (user_id),
                INDEX idx_status (status),
                INDEX idx_order_number (order_number)
            )
        ");
        echo "<p class='success'>✅ Orders table created</p>";
    } else {
        echo "<p class='success'>✅ Orders table exists</p>";
    }
    
    // Check and create order_items table
    $stmt = $pdo->query("SHOW TABLES LIKE 'order_items'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Order items table missing, creating...</p>";
        $pdo->exec("
            CREATE TABLE order_items (
                id INT PRIMARY KEY AUTO_INCREMENT,
                order_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                total DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                INDEX idx_order (order_id),
                INDEX idx_product (product_id)
            )
        ");
        echo "<p class='success'>✅ Order items table created</p>";
    } else {
        echo "<p class='success'>✅ Order items table exists</p>";
    }
    
    // Check and create products table
    $stmt = $pdo->query("SHOW TABLES LIKE 'products'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Products table missing, creating...</p>";
        $pdo->exec("
            CREATE TABLE products (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                short_description VARCHAR(500),
                price DECIMAL(10,2) NOT NULL,
                sale_price DECIMAL(10,2) NULL,
                sku VARCHAR(100) UNIQUE,
                stock_quantity INT DEFAULT 0,
                category_id INT,
                image VARCHAR(255),
                gallery TEXT,
                status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active',
                featured BOOLEAN DEFAULT FALSE,
                weight DECIMAL(8,2) DEFAULT 0,
                dimensions VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_status (status),
                INDEX idx_featured (featured)
            )
        ");
        echo "<p class='success'>✅ Products table created</p>";
    } else {
        echo "<p class='success'>✅ Products table exists</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Create Test User</h2>";
    
    // Check if test user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $test_user = $stmt->fetch();
    
    if (!$test_user) {
        echo "<p class='warning'>⚠️ Test user not found, creating...</p>";
        $password = password_hash('password123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, first_name, last_name, phone, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute(['<EMAIL>', $password, 'Test', 'User', '081234567890', 'active']);
        $test_user_id = $pdo->lastInsertId();
        echo "<p class='success'>✅ Test user created with ID: $test_user_id</p>";
    } else {
        $test_user_id = $test_user['id'];
        echo "<p class='success'>✅ Test user exists with ID: $test_user_id</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Create Sample Products</h2>";
    
    // Check if products exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $product_count = $stmt->fetchColumn();
    
    if ($product_count == 0) {
        echo "<p class='warning'>⚠️ No products found, creating sample products...</p>";
        $products = [
            ['Samsung Galaxy S24', 'samsung-galaxy-s24', 'Latest Samsung flagship smartphone', 'Premium smartphone with advanced features', 15000000, 'SGS24-001'],
            ['iPhone 15 Pro', 'iphone-15-pro', 'Apple iPhone 15 Pro', 'Latest iPhone with Pro features', 18000000, 'IP15P-001'],
            ['MacBook Air M2', 'macbook-air-m2', 'Apple MacBook Air with M2 chip', 'Lightweight laptop for professionals', 20000000, 'MBA-M2-001']
        ];
        
        foreach ($products as $product) {
            $stmt = $pdo->prepare("
                INSERT INTO products (name, slug, description, short_description, price, sku, stock_quantity, status, featured) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $product[0], $product[1], $product[2], $product[3], $product[4], $product[5], 10, 'active', 1
            ]);
        }
        echo "<p class='success'>✅ Sample products created</p>";
    } else {
        echo "<p class='success'>✅ Found $product_count products</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Create Sample Orders</h2>";
    
    // Check if orders exist for test user
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
    $stmt->execute([$test_user_id]);
    $order_count = $stmt->fetchColumn();
    
    if ($order_count == 0) {
        echo "<p class='warning'>⚠️ No orders found for test user, creating sample orders...</p>";
        
        // Get product IDs
        $stmt = $pdo->query("SELECT id, name, price FROM products LIMIT 3");
        $products = $stmt->fetchAll();
        
        if (!empty($products)) {
            $orders_data = [
                [
                    'order_number' => 'TWN-' . date('Y') . '-' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                    'status' => 'delivered',
                    'payment_status' => 'paid',
                    'payment_method' => 'Bank Transfer BCA',
                    'total_amount' => $products[0]['price'],
                    'shipping_amount' => 25000,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-7 days'))
                ],
                [
                    'order_number' => 'TWN-' . date('Y') . '-' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                    'status' => 'shipped',
                    'payment_status' => 'paid',
                    'payment_method' => 'GoPay',
                    'total_amount' => $products[1]['price'],
                    'shipping_amount' => 0,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                ],
                [
                    'order_number' => 'TWN-' . date('Y') . '-' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                    'status' => 'processing',
                    'payment_status' => 'paid',
                    'payment_method' => 'OVO',
                    'total_amount' => $products[2]['price'],
                    'shipping_amount' => 15000,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                ]
            ];
            
            foreach ($orders_data as $index => $order) {
                $stmt = $pdo->prepare("
                    INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount, 
                                      payment_method, payment_status, shipping_address, billing_address, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $shipping_address = "Test User\nJl. Sudirman No. 123\nJakarta Pusat, DKI Jakarta 10110\nPhone: 081234567890";
                
                $stmt->execute([
                    $order['order_number'],
                    $test_user_id,
                    $order['status'],
                    $order['total_amount'],
                    $order['shipping_amount'],
                    $order['payment_method'],
                    $order['payment_status'],
                    $shipping_address,
                    $shipping_address,
                    $order['created_at']
                ]);
                
                $order_id = $pdo->lastInsertId();
                
                // Add order item
                $product = $products[$index];
                $stmt = $pdo->prepare("
                    INSERT INTO order_items (order_id, product_id, quantity, price, total) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$order_id, $product['id'], 1, $product['price'], $product['price']]);
                
                echo "<p class='success'>✅ Created order: {$order['order_number']} - {$order['status']}</p>";
            }
        }
    } else {
        echo "<p class='success'>✅ Test user already has $order_count orders</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Auto-Login Test User</h2>";
    
    // Auto-login the test user
    $_SESSION['user_id'] = $test_user_id;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
    
    echo "<p class='success'>✅ Auto-logged in test user</p>";
    echo "<p class='info'>Session user_id: " . $_SESSION['user_id'] . "</p>";
    echo "<p class='info'>Session user_name: " . $_SESSION['user_name'] . "</p>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 6: Test My Orders Query</h2>";
    
    // Test the exact query from my-orders.php
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as item_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.user_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$test_user_id]);
    $test_orders = $stmt->fetchAll();
    
    if (!empty($test_orders)) {
        echo "<p class='success'>✅ Query successful! Found " . count($test_orders) . " orders</p>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped table-sm'>";
        echo "<thead><tr><th>Order Number</th><th>Status</th><th>Total</th><th>Items</th><th>Products</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($test_orders as $order) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td><span class='badge bg-primary'>" . htmlspecialchars($order['status']) . "</span></td>";
            echo "<td>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td>" . $order['item_count'] . "</td>";
            echo "<td>" . htmlspecialchars($order['product_names']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<p class='error'>❌ Query returned no results</p>";
    }
    echo "</div>";
    
    echo "<div class='alert alert-success text-center'>";
    echo "<h3>🎉 Fix Complete!</h3>";
    echo "<p>Everything is now set up and working. You should be able to access My Orders page.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-5'>";
echo "<h4 class='mb-4'>Test Your My Orders Page Now!</h4>";
echo "<div class='d-flex justify-content-center gap-3 flex-wrap'>";
echo "<a href='my-orders.php' class='btn btn-success btn-lg'><i class='fas fa-shopping-bag me-2'></i>My Orders Page</a>";
echo "<a href='quick-access-my-orders.php' class='btn btn-primary btn-lg'><i class='fas fa-rocket me-2'></i>Quick Access</a>";
echo "<a href='my-orders-debug.php' class='btn btn-info btn-lg'><i class='fas fa-bug me-2'></i>Debug Version</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
