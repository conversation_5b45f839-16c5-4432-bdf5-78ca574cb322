<?php
// Test All Payment Methods
require_once 'config/config.php';

echo "<h1>💳 Payment Methods Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px;'>";

$payment_methods = [
    'Bank Transfer BCA',
    'Bank Transfer Mandiri', 
    'GoPay',
    'OVO',
    'Dana',
    'Credit Card'
];

try {
    $pdo = getDBConnection();
    
    echo "<h2>Creating Test Orders for Each Payment Method</h2>";
    
    foreach ($payment_methods as $index => $payment_method) {
        echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; background: #f8f9fa;'>";
        echo "<h3>Testing: $payment_method</h3>";
        
        // Clear cart first
        $session_id = session_id() . '_' . $index;
        $pdo->exec("DELETE FROM cart WHERE session_id = '$session_id' AND user_id IS NULL");
        
        // Add a test product to cart
        $stmt = $pdo->query("SELECT id, name, price, sale_price FROM products WHERE status = 'active' LIMIT 1");
        $product = $stmt->fetch();
        
        if ($product) {
            $stmt = $pdo->prepare("INSERT INTO cart (session_id, product_id, quantity, created_at) VALUES (?, ?, 1, NOW())");
            $stmt->execute([$session_id, $product['id']]);
            
            // Calculate totals
            $price = $product['sale_price'] ?: $product['price'];
            $subtotal = $price;
            $shipping_fee = $subtotal >= 500000 ? 0 : 25000;
            $tax_amount = $subtotal * 0.11;
            $total_amount = $subtotal + $shipping_fee + $tax_amount;
            
            // Create order
            $pdo->beginTransaction();
            
            $order_number = 'TWN-' . date('Y') . '-' . str_pad(($index + 1) * 1000 + rand(1, 999), 4, '0', STR_PAD_LEFT);
            
            $shipping_address = "Test Customer\nJl. Test No. 123\nJakarta, DKI Jakarta 12345\nIndonesia\nPhone: 081234567890";
            $customer_info = json_encode([
                'first_name' => 'Test',
                'last_name' => 'Customer',
                'email' => '<EMAIL>',
                'phone' => '081234567890'
            ]);
            
            $stmt = $pdo->prepare("
                INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount,
                                  tax_amount, discount_amount, payment_method, payment_status,
                                  shipping_address, notes, coupon_id, customer_info)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $order_number, null, 'pending', $total_amount, $shipping_fee, $tax_amount,
                0, $payment_method, 'pending',
                $shipping_address, "Test order for $payment_method", null, $customer_info
            ]);
            
            $order_id = $pdo->lastInsertId();
            
            // Add order item
            $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price, total) VALUES (?, ?, 1, ?, ?)");
            $stmt->execute([$order_id, $product['id'], $price, $price]);
            
            // Clear test cart
            $stmt = $pdo->prepare("DELETE FROM cart WHERE session_id = ? AND user_id IS NULL");
            $stmt->execute([$session_id]);
            
            $pdo->commit();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p style='color: #155724; margin: 0;'><strong>✅ Order Created Successfully!</strong></p>";
            echo "<p style='margin: 5px 0;'>Order Number: <strong>$order_number</strong></p>";
            echo "<p style='margin: 5px 0;'>Product: {$product['name']}</p>";
            echo "<p style='margin: 5px 0;'>Total: " . formatPrice($total_amount) . "</p>";
            echo "<p style='margin: 5px 0;'>Payment Method: <strong>$payment_method</strong></p>";
            echo "<a href='" . SITE_URL . "/order-confirmation.php?order=$order_number' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block; margin-top: 10px;'>View Order Confirmation</a>";
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ No products available for testing</p>";
        }
        
        echo "</div>";
    }
    
    echo "<h2>📊 Payment Method Summary</h2>";
    
    // Get orders by payment method
    $stmt = $pdo->query("
        SELECT payment_method, COUNT(*) as order_count, SUM(total_amount) as total_revenue
        FROM orders 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY payment_method
        ORDER BY order_count DESC
    ");
    $payment_stats = $stmt->fetchAll();
    
    if (!empty($payment_stats)) {
        echo "<div style='overflow-x: auto; margin: 20px 0;'>";
        echo "<table style='width: 100%; border-collapse: collapse; background: white;'>";
        echo "<thead>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 12px; border: 1px solid #ddd; text-align: left;'>Payment Method</th>";
        echo "<th style='padding: 12px; border: 1px solid #ddd; text-align: center;'>Orders</th>";
        echo "<th style='padding: 12px; border: 1px solid #ddd; text-align: right;'>Total Revenue</th>";
        echo "<th style='padding: 12px; border: 1px solid #ddd; text-align: center;'>Status</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody>";
        
        foreach ($payment_stats as $stat) {
            echo "<tr>";
            echo "<td style='padding: 12px; border: 1px solid #ddd;'>";
            
            // Add appropriate icon
            if (strpos($stat['payment_method'], 'Bank Transfer') !== false) {
                echo "<i class='fas fa-university' style='color: #007bff; margin-right: 8px;'></i>";
            } elseif (in_array($stat['payment_method'], ['GoPay', 'OVO', 'Dana'])) {
                echo "<i class='fas fa-mobile-alt' style='color: #28a745; margin-right: 8px;'></i>";
            } elseif ($stat['payment_method'] === 'Credit Card') {
                echo "<i class='fas fa-credit-card' style='color: #ffc107; margin-right: 8px;'></i>";
            }
            
            echo $stat['payment_method'];
            echo "</td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center;'>{$stat['order_count']}</td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: right;'>" . formatPrice($stat['total_revenue']) . "</td>";
            echo "<td style='padding: 12px; border: 1px solid #ddd; text-align: center;'>";
            echo "<span style='background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;'>Active</span>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</tbody>";
        echo "</table>";
        echo "</div>";
    }
    
    echo "<h2>🎯 Payment Method Features</h2>";
    
    $method_features = [
        'Bank Transfer BCA' => [
            'type' => 'Bank Transfer',
            'processing' => '1-2 business days',
            'fees' => 'No additional fees',
            'verification' => 'Manual verification required',
            'popularity' => 'High',
            'icon' => 'fas fa-university',
            'color' => '#007bff'
        ],
        'Bank Transfer Mandiri' => [
            'type' => 'Bank Transfer',
            'processing' => '1-2 business days',
            'fees' => 'No additional fees',
            'verification' => 'Manual verification required',
            'popularity' => 'High',
            'icon' => 'fas fa-university',
            'color' => '#007bff'
        ],
        'GoPay' => [
            'type' => 'E-Wallet',
            'processing' => 'Instant',
            'fees' => 'No additional fees',
            'verification' => 'Automatic via QR code',
            'popularity' => 'Very High',
            'icon' => 'fas fa-mobile-alt',
            'color' => '#28a745'
        ],
        'OVO' => [
            'type' => 'E-Wallet',
            'processing' => 'Instant',
            'fees' => 'No additional fees',
            'verification' => 'Automatic via QR code',
            'popularity' => 'High',
            'icon' => 'fas fa-mobile-alt',
            'color' => '#28a745'
        ],
        'Dana' => [
            'type' => 'E-Wallet',
            'processing' => 'Instant',
            'fees' => 'No additional fees',
            'verification' => 'Automatic via QR code',
            'popularity' => 'Medium',
            'icon' => 'fas fa-mobile-alt',
            'color' => '#28a745'
        ],
        'Credit Card' => [
            'type' => 'Credit Card',
            'processing' => 'Instant',
            'fees' => '2.9% + Rp 2,000',
            'verification' => 'Automatic via payment gateway',
            'popularity' => 'Medium',
            'icon' => 'fas fa-credit-card',
            'color' => '#ffc107'
        ]
    ];
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    foreach ($method_features as $method => $features) {
        echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: white;'>";
        echo "<h4 style='margin-top: 0; color: {$features['color']};'>";
        echo "<i class='{$features['icon']}' style='margin-right: 8px;'></i>$method";
        echo "</h4>";
        
        echo "<div style='margin: 15px 0;'>";
        echo "<p style='margin: 5px 0;'><strong>Type:</strong> {$features['type']}</p>";
        echo "<p style='margin: 5px 0;'><strong>Processing:</strong> {$features['processing']}</p>";
        echo "<p style='margin: 5px 0;'><strong>Fees:</strong> {$features['fees']}</p>";
        echo "<p style='margin: 5px 0;'><strong>Verification:</strong> {$features['verification']}</p>";
        echo "<p style='margin: 5px 0;'><strong>Popularity:</strong> ";
        
        $popularity_colors = [
            'Very High' => '#28a745',
            'High' => '#007bff', 
            'Medium' => '#ffc107'
        ];
        $pop_color = $popularity_colors[$features['popularity']] ?? '#6c757d';
        echo "<span style='background: $pop_color; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;'>{$features['popularity']}</span>";
        echo "</p>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 30px 0; text-align: center;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ All Payment Methods Successfully Tested!</h3>";
    echo "<p style='color: #155724; margin: 10px 0;'>The checkout system supports all major Indonesian payment methods with proper payment instructions and order processing.</p>";
    echo "<a href='" . SITE_URL . "/checkout_process_documentation.php' style='background: #155724; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px;'>View Full Documentation</a>";
    echo "<a href='" . SITE_URL . "/checkout.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px;'>Test Checkout Process</a>";
    echo "</div>";
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4 style='color: #721c24; margin-top: 0;'>❌ Error Testing Payment Methods</h4>";
    echo "<p style='color: #721c24; margin: 0;'>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "</div>";
?>
