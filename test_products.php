<?php
// Test Products Page Functionality
require_once 'config/config.php';

echo "<h1>🛍️ Products Page Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px;'>";

try {
    echo "<h2>1. Database Connection Test</h2>";
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✓ Database connected successfully</p>";
    
    echo "<h2>2. Categories Test</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE status = 'active'");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✓ Found " . $result['count'] . " active categories</p>";
    
    if ($result['count'] > 0) {
        $stmt = $pdo->query("SELECT * FROM categories WHERE status = 'active' ORDER BY name LIMIT 5");
        $categories = $stmt->fetchAll();
        echo "<p><strong>Sample categories:</strong></p>";
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>" . htmlspecialchars($category['name']) . " (slug: " . htmlspecialchars($category['slug']) . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>3. Products Test</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE status = 'active'");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✓ Found " . $result['count'] . " active products</p>";
    
    if ($result['count'] > 0) {
        $stmt = $pdo->query("
            SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE p.status = 'active' 
            ORDER BY p.created_at DESC 
            LIMIT 5
        ");
        $products = $stmt->fetchAll();
        echo "<p><strong>Sample products:</strong></p>";
        echo "<ul>";
        foreach ($products as $product) {
            echo "<li>" . htmlspecialchars($product['name']) . " - Rp " . number_format($product['price']) . " (" . htmlspecialchars($product['category_name'] ?? 'No category') . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>4. Products Page Variables Test</h2>";
    
    // Test different scenarios
    $test_scenarios = [
        ['description' => 'All products', 'params' => ''],
        ['description' => 'Electronics category', 'params' => '?category=electronics'],
        ['description' => 'Search query', 'params' => '?q=phone'],
        ['description' => 'Sort by price', 'params' => '?sort=price_low'],
    ];
    
    foreach ($test_scenarios as $scenario) {
        echo "<h4>Testing: " . $scenario['description'] . "</h4>";
        
        // Simulate the products.php logic
        parse_str(parse_url($scenario['params'], PHP_URL_QUERY) ?? '', $get_params);
        
        $category_slug = $get_params['category'] ?? '';
        $search_query = $get_params['q'] ?? '';
        $sort_by = $get_params['sort'] ?? 'newest';
        
        // Initialize current_category
        $current_category = null;
        
        if ($category_slug) {
            $stmt = $pdo->prepare("SELECT * FROM categories WHERE slug = ?");
            $stmt->execute([$category_slug]);
            $current_category = $stmt->fetch();
        }
        
        // Test the variables that were causing issues
        echo "<p>Category slug: " . ($category_slug ?: 'none') . "</p>";
        echo "<p>Search query: " . ($search_query ?: 'none') . "</p>";
        echo "<p>Sort by: " . $sort_by . "</p>";
        echo "<p>Current category: " . ($current_category ? $current_category['name'] : 'null') . "</p>";
        
        // Test the problematic lines from products.php
        $page_title = '';
        if ($current_category) {
            $page_title = htmlspecialchars($current_category['name']);
        } elseif ($search_query) {
            $page_title = 'Search Results for "' . htmlspecialchars($search_query) . '"';
        } else {
            $page_title = 'All Products';
        }
        
        echo "<p style='color: green;'>✓ Page title: " . $page_title . "</p>";
        echo "<hr>";
    }
    
    echo "<h2>5. Products Page Links Test</h2>";
    echo "<p><a href='products.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test All Products</a></p>";
    
    if ($result['count'] > 0) {
        // Get a sample category for testing
        $stmt = $pdo->query("SELECT slug FROM categories WHERE status = 'active' LIMIT 1");
        $sample_category = $stmt->fetch();
        
        if ($sample_category) {
            echo "<p><a href='products.php?category=" . $sample_category['slug'] . "' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Category: " . $sample_category['slug'] . "</a></p>";
        }
        
        echo "<p><a href='products.php?q=phone' target='_blank' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Search: phone</a></p>";
        echo "<p><a href='products.php?sort=price_low' target='_blank' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Sort: Price Low to High</a></p>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724;'>✅ Products Page Fix Complete!</h3>";
    echo "<p><strong>Fixed Issues:</strong></p>";
    echo "<ul>";
    echo "<li>✓ Undefined variable \$current_category - Now properly initialized</li>";
    echo "<li>✓ Exception handling - Variables defined in catch block</li>";
    echo "<li>✓ Safety checks - Variables initialized at start</li>";
    echo "</ul>";
    echo "<p><strong>The products page should now work without warnings!</strong></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ ERROR: " . $e->getMessage() . "</p>";
    echo "<p>Please ensure:</p>";
    echo "<ul>";
    echo "<li>Database is properly set up</li>";
    echo "<li>Tables exist (run verify_database.php if needed)</li>";
    echo "<li>XAMPP MySQL is running</li>";
    echo "</ul>";
}

echo "</div>";
?>
