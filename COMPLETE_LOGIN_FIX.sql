-- COMPLETE LOGIN FIX - Run this SQL script in phpMyAdmin or SQLyog
-- This will create a working database with proper structure

-- Step 1: Drop and recreate database to ensure clean state
DROP DATABASE IF EXISTS `db_tewuneed2`;
CREATE DATABASE `db_tewuneed2` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `db_tewuneed2`;

-- Step 2: Create users table with exact structure needed
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 3: Create categories table (needed for index.php)
CREATE TABLE `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 4: Create products table (needed for index.php)
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `short_description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `sale_price` decimal(10,2) DEFAULT NULL,
  `sku` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stock_quantity` int(11) DEFAULT 0,
  `category_id` int(11) DEFAULT NULL,
  `images` json DEFAULT NULL,
  `featured` tinyint(1) DEFAULT 0,
  `status` enum('active','inactive','draft') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`featured`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Step 5: Insert test users with properly hashed passwords
-- Password for all users is: user123
INSERT INTO `users` (`email`, `password`, `first_name`, `last_name`, `phone`, `email_verified`, `status`) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Test', 'User', '081234567890', 1, 'active'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', '081234567891', 1, 'active'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', '081234567892', 1, 'active');

-- Step 6: Insert sample categories
INSERT INTO `categories` (`name`, `slug`, `description`, `status`) VALUES
('Electronics', 'electronics', 'Electronic devices and gadgets', 'active'),
('Cosmetics', 'cosmetics', 'Beauty and cosmetic products', 'active'),
('Sports', 'sports', 'Sports and fitness equipment', 'active'),
('Food & Snacks', 'food-snacks', 'Food items and snacks', 'active');

-- Step 7: Insert sample products
INSERT INTO `products` (`name`, `slug`, `description`, `short_description`, `price`, `sale_price`, `sku`, `stock_quantity`, `category_id`, `featured`, `status`) VALUES
('iPhone 14 Pro', 'iphone-14-pro', 'Latest iPhone with advanced camera system and A16 Bionic chip', 'Premium smartphone with excellent camera', 14000000.00, 13500000.00, 'IP14PRO001', 25, 1, 1, 'active'),
('Samsung Galaxy S23', 'samsung-galaxy-s23', 'Flagship Android smartphone with Snapdragon processor', 'High-performance Android device', 12000000.00, 11500000.00, 'SGS23001', 30, 1, 1, 'active'),
('MacBook Air M2', 'macbook-air-m2', 'Ultra-thin laptop with M2 chip and Retina display', 'Powerful and portable laptop', 18000000.00, 17000000.00, 'MBA2001', 15, 1, 1, 'active'),
('Vitamin C Serum', 'vitamin-c-serum', 'Anti-aging serum with 20% Vitamin C for bright skin', 'Brightening skincare serum', 250000.00, 199000.00, 'VCS001', 100, 2, 1, 'active'),
('Dumbbell Set 20kg', 'dumbbell-set-20kg', 'Adjustable dumbbell set perfect for home workouts', 'Home gym equipment', 800000.00, 750000.00, 'DS20001', 20, 3, 1, 'active'),
('Premium Coffee', 'premium-coffee', 'Single-origin arabica coffee beans with rich flavor', 'High-quality coffee beans', 180000.00, 160000.00, 'PC001', 50, 4, 1, 'active');

-- Step 8: Verify data insertion
SELECT 'Users created:' as info, COUNT(*) as count FROM users;
SELECT 'Categories created:' as info, COUNT(*) as count FROM categories;
SELECT 'Products created:' as info, COUNT(*) as count FROM products;

-- Step 9: Test login query (this should return user data)
SELECT id, email, first_name, last_name, status 
FROM users 
WHERE email = '<EMAIL>' AND status = 'active';

-- Database setup complete!
-- You can now test login with:
-- Email: <EMAIL>, Password: user123
-- Email: <EMAIL>, Password: user123  
-- Email: <EMAIL>, Password: user123
