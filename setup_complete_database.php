<?php
// Complete Database Setup and Product Import
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🚀 Complete Website Setup</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px;'>";

try {
    // Database configuration
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'db_tewuneed2';
    
    echo "<h2>Step 1: Database Connection</h2>";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ MySQL connected</p>";
    
    // Drop and recreate database
    echo "<h2>Step 2: Database Recreation</h2>";
    $pdo->exec("DROP DATABASE IF EXISTS `$database`");
    $pdo->exec("CREATE DATABASE `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$database`");
    echo "<p style='color: green;'>✓ Database recreated: $database</p>";
    
    // Read and execute the database setup SQL
    echo "<h2>Step 3: Running Database Setup</h2>";
    $sql_file = __DIR__ . '/database_setup.sql';
    
    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);
        
        // Remove the database creation parts since we already did that
        $sql_content = preg_replace('/CREATE DATABASE.*?;/i', '', $sql_content);
        $sql_content = preg_replace('/USE.*?;/i', '', $sql_content);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql_content)));
        
        $executed = 0;
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                try {
                    $pdo->exec($statement);
                    $executed++;
                } catch (Exception $e) {
                    // Skip errors for duplicate entries or existing tables
                    if (!strpos($e->getMessage(), 'Duplicate entry') && 
                        !strpos($e->getMessage(), 'already exists')) {
                        echo "<p style='color: orange;'>Warning: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
        echo "<p style='color: green;'>✓ Executed $executed SQL statements</p>";
    } else {
        echo "<p style='color: red;'>✗ database_setup.sql file not found</p>";
    }
    
    // Verify tables and data
    echo "<h2>Step 4: Verification</h2>";
    
    $tables = ['categories', 'products', 'users', 'orders', 'reviews'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch()['count'];
        echo "<p style='color: green;'>✓ Table '$table': $count records</p>";
    }
    
    // Test product query (same as products.php)
    echo "<h2>Step 5: Product Query Test</h2>";
    $stmt = $pdo->prepare("
        SELECT p.*, c.name as category_name, c.slug as category_slug
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    echo "<p style='color: green;'>✓ Found " . count($products) . " active products</p>";
    
    if (count($products) > 0) {
        echo "<h3>Sample Products:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Price</th><th>Category</th><th>Stock</th><th>Status</th></tr>";
        foreach (array_slice($products, 0, 5) as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>Rp " . number_format($product['price']) . "</td>";
            echo "<td>" . htmlspecialchars($product['category_name'] ?? 'No category') . "</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "<td>" . $product['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Create uploads directory structure
    echo "<h2>Step 6: Directory Setup</h2>";
    $directories = [
        'uploads',
        'uploads/products',
        'uploads/categories',
        'uploads/users',
        'uploads/temp'
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
            echo "<p style='color: green;'>✓ Created directory: $dir</p>";
        } else {
            echo "<p style='color: blue;'>→ Directory exists: $dir</p>";
        }
    }
    
    // Create default product images
    echo "<h2>Step 7: Default Images Setup</h2>";
    $default_image_content = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    
    if (!file_exists('uploads/products/default-product.jpg')) {
        file_put_contents('uploads/products/default-product.jpg', $default_image_content);
        echo "<p style='color: green;'>✓ Created default product image</p>";
    }
    
    // Update config to use correct database
    echo "<h2>Step 8: Configuration Update</h2>";
    $config_file = 'config/database.php';
    if (file_exists($config_file)) {
        $config_content = file_get_contents($config_file);
        $config_content = preg_replace("/define\('DB_NAME',\s*'[^']*'\);/", "define('DB_NAME', '$database');", $config_content);
        file_put_contents($config_file, $config_content);
        echo "<p style='color: green;'>✓ Updated database configuration</p>";
    }
    
    // Test final connection
    echo "<h2>Step 9: Final Test</h2>";
    require_once 'config/config.php';
    
    try {
        $test_pdo = getDBConnection();
        $stmt = $test_pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
        $total = $stmt->fetch()['total'];
        echo "<p style='color: green;'>✓ Final test: $total active products found</p>";
        
        // Test categories
        $stmt = $test_pdo->query("SELECT COUNT(*) as total FROM categories WHERE status = 'active'");
        $total_cats = $stmt->fetch()['total'];
        echo "<p style='color: green;'>✓ Final test: $total_cats active categories found</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Final test failed: " . $e->getMessage() . "</p>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2 style='color: #155724;'>🎉 SETUP COMPLETE!</h2>";
    echo "<p><strong>Database Setup:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database: db_tewuneed2 created</li>";
    echo "<li>✅ All tables created with proper structure</li>";
    echo "<li>✅ 50+ products imported</li>";
    echo "<li>✅ 6 categories created</li>";
    echo "<li>✅ Sample users, orders, reviews added</li>";
    echo "<li>✅ Upload directories created</li>";
    echo "<li>✅ Configuration updated</li>";
    echo "</ul>";
    
    echo "<p><strong>Test Your Website:</strong></p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Home Page</a></p>";
    echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🛍️ Products Page</a></p>";
    echo "<p><a href='login_firebase.php' style='background: #ffc107; color: black; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 Firebase Login</a></p>";
    echo "<p><a href='firebase_register.php' style='background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>📝 Firebase Register</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ SETUP ERROR: " . $e->getMessage() . "</p>";
    echo "<p>Please ensure:</p>";
    echo "<ul>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>database_setup.sql file exists</li>";
    echo "<li>Proper file permissions</li>";
    echo "</ul>";
}

echo "</div>";
?>
