<?php
// Debug Products Page Issues
require_once 'config/config.php';

echo "<h1>🔍 Debug Products Page</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px;'>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✓ Database connected</p>";
    
    // Check if tables exist
    echo "<h2>Table Structure Check</h2>";
    $tables = ['categories', 'products', 'users'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("DESCRIBE $table");
            $columns = $stmt->fetchAll();
            echo "<p style='color: green;'>✓ Table '$table' exists with " . count($columns) . " columns</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Table '$table' missing: " . $e->getMessage() . "</p>";
        }
    }
    
    // Check data counts
    echo "<h2>Data Count Check</h2>";
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch()['count'];
            echo "<p style='color: " . ($count > 0 ? 'green' : 'red') . ";'>$table: $count records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error counting $table: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test exact products.php query
    echo "<h2>Products.php Query Simulation</h2>";
    
    // Simulate the exact conditions from products.php
    $category_slug = $_GET['category'] ?? '';
    $search_query = $_GET['q'] ?? '';
    $sort_by = $_GET['sort'] ?? 'newest';
    $page = (int)($_GET['page'] ?? 1);
    $offset = ($page - 1) * PRODUCTS_PER_PAGE;
    
    // Build where clause (same as products.php)
    $where_conditions = ["p.status = 'active'"];
    $params = [];
    
    if ($category_slug) {
        $where_conditions[] = "c.slug = ?";
        $params[] = $category_slug;
    }
    
    if ($search_query) {
        $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ? OR p.short_description LIKE ?)";
        $search_param = '%' . $search_query . '%';
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Build order clause
    switch ($sort_by) {
        case 'oldest':
            $order_by = "p.created_at ASC";
            break;
        case 'name':
            $order_by = "p.name ASC";
            break;
        case 'price_low':
            $order_by = "COALESCE(p.sale_price, p.price) ASC";
            break;
        case 'price_high':
            $order_by = "COALESCE(p.sale_price, p.price) DESC";
            break;
        default:
            $order_by = "p.created_at DESC";
    }
    
    echo "<p><strong>Parameters:</strong></p>";
    echo "<ul>";
    echo "<li>Category: " . ($category_slug ?: 'All') . "</li>";
    echo "<li>Search: " . ($search_query ?: 'None') . "</li>";
    echo "<li>Sort: $sort_by</li>";
    echo "<li>Page: $page</li>";
    echo "</ul>";
    
    echo "<p><strong>Where clause:</strong> <code>$where_clause</code></p>";
    echo "<p><strong>Order by:</strong> <code>$order_by</code></p>";
    echo "<p><strong>Parameters:</strong> <code>" . json_encode($params) . "</code></p>";
    
    // Test count query
    $count_sql = "
        SELECT COUNT(*) as total 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE {$where_clause}
    ";
    
    echo "<p><strong>Count Query:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$count_sql</pre>";
    
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_products = $stmt->fetch()['total'];
    
    echo "<p><strong>Count Result:</strong> $total_products products found</p>";
    
    if ($total_products > 0) {
        // Test main query
        $sql = "
            SELECT p.*, c.name as category_name, c.slug as category_slug
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            WHERE {$where_clause}
            ORDER BY {$order_by}
            LIMIT " . PRODUCTS_PER_PAGE . " OFFSET {$offset}
        ";
        
        echo "<p><strong>Main Query:</strong></p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$sql</pre>";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $products = $stmt->fetchAll();
        
        echo "<p><strong>Main Query Result:</strong> " . count($products) . " products returned</p>";
        
        if (count($products) > 0) {
            echo "<h3>Sample Products:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Name</th>";
            echo "<th style='padding: 8px;'>Price</th>";
            echo "<th style='padding: 8px;'>Category</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach (array_slice($products, 0, 5) as $product) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>" . $product['id'] . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($product['name']) . "</td>";
                echo "<td style='padding: 8px;'>Rp " . number_format($product['price']) . "</td>";
                echo "<td style='padding: 8px;'>" . htmlspecialchars($product['category_name'] ?? 'No category') . "</td>";
                echo "<td style='padding: 8px;'>" . $product['status'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3 style='color: #155724;'>✅ SUCCESS!</h3>";
            echo "<p>The query is working correctly. Products should display on the products page.</p>";
            echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛍️ Test Products Page</a></p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24;'>❌ NO PRODUCTS FOUND</h3>";
        echo "<p>The database query is not returning any products. This could be because:</p>";
        echo "<ul>";
        echo "<li>No products have status = 'active'</li>";
        echo "<li>Database is empty</li>";
        echo "<li>Table structure is incorrect</li>";
        echo "</ul>";
        echo "<p><a href='fix_database_now.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Fix Database</a></p>";
        echo "</div>";
    }
    
    // Show all products regardless of status
    echo "<h2>All Products (Any Status)</h2>";
    $stmt = $pdo->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id LIMIT 10");
    $all_products = $stmt->fetchAll();
    
    if (count($all_products) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Name</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Category</th>";
        echo "</tr>";
        
        foreach ($all_products as $product) {
            $status_color = $product['status'] === 'active' ? 'green' : 'red';
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $product['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td style='padding: 8px; color: $status_color;'>" . $product['status'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['category_name'] ?? 'No category') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>No products found in database at all!</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Database Error</h3>";
    echo "<p style='color: #721c24;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 0; padding: 20px; }
table { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
th { background: #007bff !important; color: white !important; }
tr:nth-child(even) { background: #f8f9fa; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
