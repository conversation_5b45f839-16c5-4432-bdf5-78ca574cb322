<?php
require_once 'config/config.php';

$page_title = 'Debug Order Button';
$page_description = 'Testing the Confirm & Place Order button';

// Simulate checkout data for testing
if (!isset($_SESSION['checkout_data'])) {
    $_SESSION['checkout_data'] = [
        'first_name' => '<PERSON>',
        'last_name' => 'Baringbing',
        'email' => '<EMAIL>',
        'phone' => '************',
        'address_line_1' => 'Jl. Test No. 123',
        'address_line_2' => '',
        'city' => 'Jakarta',
        'state' => 'DKI Jakarta',
        'postal_code' => '12345',
        'country' => 'Indonesia',
        'payment_method' => 'bank_transfer',
        'notes' => 'Test order',
        'coupon_code' => '',
        'discount_amount' => 0,
        'coupon_id' => null
    ];
}

// Simulate cart items
$cart_items = [
    [
        'product' => [
            'id' => 1,
            'name' => 'Test Product',
            'price' => 100000,
            'sale_price' => null,
            'image' => null,
            'slug' => 'test-product'
        ],
        'quantity' => 2,
        'price' => 100000,
        'total' => 200000
    ]
];

$subtotal = 200000;
$shipping_fee = 15000;
$tax_amount = 20000;
$discount_amount = 0;
$total_amount = $subtotal + $shipping_fee + $tax_amount - $discount_amount;

// Handle form submission
$debug_messages = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_order'])) {
    $debug_messages[] = "✅ Form submitted successfully";
    $debug_messages[] = "📝 POST data received: " . print_r($_POST, true);
    $debug_messages[] = "🔄 Processing order...";
    
    try {
        $pdo = getDBConnection();
        $debug_messages[] = "✅ Database connection established";
        
        // Generate order number
        $order_number = 'TWN-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $debug_messages[] = "📋 Generated order number: " . $order_number;
        
        // Test database insert (without actually inserting)
        $debug_messages[] = "🧪 Testing database insert...";
        
        // Simulate successful order creation
        $debug_messages[] = "✅ Order would be created successfully!";
        $debug_messages[] = "🎉 Order processing completed";
        $debug_messages[] = "🔄 Would redirect to order-success.php";
        
        // Don't actually redirect for testing
        $_SESSION['test_success'] = "Order would be placed successfully with number: " . $order_number;
        
    } catch (Exception $e) {
        $debug_messages[] = "❌ Error: " . $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">🐛 Debug Order Button</h1>
        <p class="lead">Testing the "Confirm & Place Order" button functionality</p>
    </div>

    <!-- Debug Messages -->
    <?php if (!empty($debug_messages)): ?>
        <div class="alert alert-info mb-4">
            <h5><i class="fas fa-bug me-2"></i>Debug Messages</h5>
            <?php foreach ($debug_messages as $message): ?>
                <div class="mb-1"><?php echo htmlspecialchars($message); ?></div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['test_success'])): ?>
        <div class="alert alert-success mb-4">
            <h5><i class="fas fa-check-circle me-2"></i>Success!</h5>
            <p class="mb-0"><?php echo $_SESSION['test_success']; unset($_SESSION['test_success']); ?></p>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($cart_items as $item): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1"><?php echo htmlspecialchars($item['product']['name']); ?></h6>
                                <small class="text-muted">Quantity: <?php echo $item['quantity']; ?></small>
                            </div>
                            <div class="text-end">
                                <span class="fw-bold"><?php echo formatPrice($item['total']); ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Customer Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> <?php echo htmlspecialchars($_SESSION['checkout_data']['first_name'] . ' ' . $_SESSION['checkout_data']['last_name']); ?></p>
                            <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['checkout_data']['email']); ?></p>
                            <p><strong>Phone:</strong> <?php echo htmlspecialchars($_SESSION['checkout_data']['phone']); ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Address:</strong><br>
                            <?php echo htmlspecialchars($_SESSION['checkout_data']['address_line_1']); ?><br>
                            <?php echo htmlspecialchars($_SESSION['checkout_data']['city'] . ', ' . $_SESSION['checkout_data']['state']); ?><br>
                            <?php echo htmlspecialchars($_SESSION['checkout_data']['postal_code']); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Total & Action -->
        <div class="col-lg-4">
            <div class="card shadow-sm sticky-top">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Order Total</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span><?php echo formatPrice($subtotal); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Shipping:</span>
                        <span><?php echo formatPrice($shipping_fee); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span><?php echo formatPrice($tax_amount); ?></span>
                    </div>
                    <?php if ($discount_amount > 0): ?>
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Discount:</span>
                            <span>-<?php echo formatPrice($discount_amount); ?></span>
                        </div>
                    <?php endif; ?>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>Total:</strong>
                        <strong class="text-primary"><?php echo formatPrice($total_amount); ?></strong>
                    </div>

                    <!-- Test Form -->
                    <form method="POST" id="orderForm">
                        <div class="d-grid gap-2">
                            <button type="submit" name="confirm_order" value="1" class="btn btn-success btn-lg" id="confirmButton">
                                <i class="fas fa-check me-2"></i>Confirm & Place Order
                            </button>
                            <a href="checkout.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Checkout
                            </a>
                        </div>
                    </form>

                    <!-- Debug Info -->
                    <div class="mt-4 p-3 bg-light border rounded">
                        <h6 class="fw-bold mb-3">Debug Information</h6>
                        <small>
                            <p><strong>Request Method:</strong> <?php echo $_SERVER['REQUEST_METHOD']; ?></p>
                            <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
                            <p><strong>POST Data:</strong> <?php echo empty($_POST) ? 'None' : 'Available'; ?></p>
                            <p><strong>Cart Items:</strong> <?php echo count($cart_items); ?></p>
                            <p><strong>Checkout Data:</strong> <?php echo isset($_SESSION['checkout_data']) ? 'Available' : 'Missing'; ?></p>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Instructions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">🧪 Testing Instructions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">What to Test</h6>
                            <ul class="small">
                                <li>Click the "Confirm & Place Order" button</li>
                                <li>Watch for loading state (spinner)</li>
                                <li>Check debug messages above</li>
                                <li>Verify form submission works</li>
                                <li>Look for any JavaScript errors in console</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Expected Results</h6>
                            <ul class="small">
                                <li>Button shows loading spinner</li>
                                <li>Form submits to same page</li>
                                <li>Debug messages appear</li>
                                <li>Success message shows</li>
                                <li>No JavaScript errors</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="text-center mt-5">
        <h4 class="mb-4">Quick Actions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="order-review.php" class="btn btn-primary">
                <i class="fas fa-eye me-2"></i>Real Order Review
            </a>
            <a href="checkout.php" class="btn btn-success">
                <i class="fas fa-shopping-cart me-2"></i>Checkout Page
            </a>
            <a href="cart.php" class="btn btn-info">
                <i class="fas fa-shopping-bag me-2"></i>View Cart
            </a>
        </div>
    </div>
</div>

<script>
// Enhanced debugging for the order form
document.addEventListener('DOMContentLoaded', function() {
    console.log('🐛 Debug script loaded');
    
    const form = document.getElementById('orderForm');
    const button = document.getElementById('confirmButton');
    
    console.log('Form found:', !!form);
    console.log('Button found:', !!button);
    
    if (form && button) {
        // Add click handler to button for debugging
        button.addEventListener('click', function(e) {
            console.log('🔘 Button clicked');
            console.log('Button disabled:', this.disabled);
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);
        });
        
        // Form submit handler with enhanced debugging
        form.addEventListener('submit', function(e) {
            console.log('📝 Form submit event triggered');
            console.log('Event target:', e.target);
            console.log('Form data:', new FormData(this));
            
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing Order...';
            button.disabled = true;
            
            console.log('🔄 Loading state applied');
            
            // Let the form submit normally
            return true;
        });
        
        console.log('✅ Event handlers attached');
    } else {
        console.error('❌ Form or button not found!');
    }
});

// Monitor for any JavaScript errors
window.addEventListener('error', function(e) {
    console.error('🚨 JavaScript Error:', e.error);
    console.error('Message:', e.message);
    console.error('File:', e.filename);
    console.error('Line:', e.lineno);
});
</script>

<?php include 'includes/footer.php'; ?>
