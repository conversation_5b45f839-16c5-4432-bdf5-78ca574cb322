<?php
// Fix Column Error - Add Missing Status Column
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Fix Column Error</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;'>";

try {
    // Database configuration
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'db_tewuneed2';
    
    echo "<h2>Step 1: Connect to Database</h2>";
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Connected to database: $database</p>";
    
    echo "<h2>Step 2: Check Current Table Structure</h2>";
    
    // Check products table structure
    $stmt = $pdo->query("DESCRIBE products");
    $columns = $stmt->fetchAll();
    
    echo "<p><strong>Current products table columns:</strong></p>";
    echo "<ul>";
    $has_status = false;
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
        if ($column['Field'] === 'status') {
            $has_status = true;
        }
    }
    echo "</ul>";
    
    if (!$has_status) {
        echo "<h2>Step 3: Add Missing Status Column</h2>";
        $pdo->exec("ALTER TABLE products ADD COLUMN status ENUM('active', 'inactive', 'draft') DEFAULT 'active'");
        echo "<p style='color: green;'>✓ Added status column to products table</p>";
        
        // Update all existing products to active
        $stmt = $pdo->exec("UPDATE products SET status = 'active' WHERE status IS NULL");
        echo "<p style='color: green;'>✓ Updated $stmt products to active status</p>";
    } else {
        echo "<p style='color: blue;'>→ Status column already exists</p>";
    }
    
    // Check categories table
    echo "<h2>Step 4: Check Categories Table</h2>";
    $stmt = $pdo->query("DESCRIBE categories");
    $cat_columns = $stmt->fetchAll();
    
    $cat_has_status = false;
    foreach ($cat_columns as $column) {
        if ($column['Field'] === 'status') {
            $cat_has_status = true;
            break;
        }
    }
    
    if (!$cat_has_status) {
        echo "<p style='color: orange;'>Adding status column to categories table...</p>";
        $pdo->exec("ALTER TABLE categories ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'");
        $stmt = $pdo->exec("UPDATE categories SET status = 'active' WHERE status IS NULL");
        echo "<p style='color: green;'>✓ Added status column to categories table</p>";
    } else {
        echo "<p style='color: green;'>✓ Categories table has status column</p>";
    }
    
    echo "<h2>Step 5: Test Fixed Query</h2>";
    
    // Test the query that was failing
    $test_sql = "
        SELECT COUNT(*) as total
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active'
    ";
    
    echo "<p><strong>Testing query:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$test_sql</pre>";
    
    $stmt = $pdo->prepare($test_sql);
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "<p style='color: green;'>✓ Query executed successfully!</p>";
    echo "<p><strong>Result:</strong> Found " . $result['total'] . " active products</p>";
    
    if ($result['total'] == 0) {
        echo "<h2>Step 6: Add Sample Products</h2>";
        
        // Add some quick sample products
        $sample_products = [
            ['iPhone 14 Pro', 'iphone-14-pro', 'Latest iPhone with advanced features', 15000000, 1],
            ['Samsung Galaxy S23', 'samsung-galaxy-s23', 'Flagship Android smartphone', 12000000, 1],
            ['MacBook Air M2', 'macbook-air-m2', 'Ultra-thin laptop with M2 chip', 18000000, 1],
            ['Serum Vitamin C', 'serum-vitamin-c', 'Anti-aging vitamin C serum', 200000, 2],
            ['Foundation Liquid', 'foundation-liquid', 'Full-coverage liquid foundation', 280000, 2],
            ['Dumbbell Set 20kg', 'dumbbell-set-20kg', 'Professional adjustable dumbbell set', 750000, 3],
            ['Premium Coffee Beans', 'premium-coffee-beans', 'Single-origin arabica coffee beans', 160000, 4],
            ['Multivitamin Complete', 'multivitamin-complete', 'Complete multivitamin supplement', 130000, 5],
            ['Fresh Broccoli', 'fresh-broccoli', 'Nutritious fresh broccoli', 10000, 6]
        ];
        
        $added = 0;
        foreach ($sample_products as $product) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO products (name, slug, description, price, category_id, stock_quantity, status, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, 50, 'active', NOW(), NOW())
                ");
                $stmt->execute($product);
                $added++;
            } catch (Exception $e) {
                // Product might already exist, skip
            }
        }
        
        echo "<p style='color: green;'>✓ Added $added sample products</p>";
        
        // Test again
        $stmt = $pdo->prepare($test_sql);
        $stmt->execute();
        $result = $stmt->fetch();
        echo "<p><strong>New total:</strong> " . $result['total'] . " active products</p>";
    }
    
    echo "<h2>Step 7: Test Full Products Query</h2>";
    
    // Test the full query from products.php
    $full_sql = "
        SELECT p.*, c.name as category_name, c.slug as category_slug
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 12
    ";
    
    echo "<p><strong>Testing full query:</strong></p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$full_sql</pre>";
    
    $stmt = $pdo->prepare($full_sql);
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    echo "<p style='color: green;'>✓ Full query executed successfully!</p>";
    echo "<p><strong>Result:</strong> Found " . count($products) . " products</p>";
    
    if (count($products) > 0) {
        echo "<h3>Sample Products:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Name</th>";
        echo "<th style='padding: 8px;'>Price</th>";
        echo "<th style='padding: 8px;'>Category</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach (array_slice($products, 0, 5) as $product) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $product['id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td style='padding: 8px;'>Rp " . number_format($product['price']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($product['category_name'] ?? 'No category') . "</td>";
            echo "<td style='padding: 8px;'>" . $product['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2 style='color: #155724;'>🎉 COLUMN ERROR FIXED!</h2>";
    echo "<p><strong>What was fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Added missing 'status' column to products table</li>";
    echo "<li>✅ Added missing 'status' column to categories table</li>";
    echo "<li>✅ Set all existing products to 'active' status</li>";
    echo "<li>✅ Verified queries are working</li>";
    echo "<li>✅ Added sample products if needed</li>";
    echo "</ul>";
    
    echo "<p><strong>Test Your Website Now:</strong></p>";
    echo "<p><a href='products.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🛍️ Test Products Page</a></p>";
    echo "<p><a href='debug_products.php' style='background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Run Debug Again</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2 style='color: #721c24;'>❌ ERROR</h2>";
    echo "<p style='color: #721c24;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Please ensure:</strong></p>";
    echo "<ul>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>Database 'db_tewuneed2' exists</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 0; padding: 20px; }
table { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
th { background: #007bff !important; color: white !important; }
tr:nth-child(even) { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
