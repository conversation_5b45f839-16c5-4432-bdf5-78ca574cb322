<?php
// Emergency Database Fix - Complete Repair
require_once 'config/config.php';

echo "<h1>🚨 Emergency Database Repair</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";

try {
    $pdo = getDBConnection();
    
    echo "<h2>🔍 Step 1: Checking Current Database Structure</h2>";
    
    // Check products table structure
    echo "<h3>Products Table:</h3>";
    $stmt = $pdo->query("DESCRIBE products");
    $product_columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    $has_product_status = false;
    foreach ($product_columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
        if ($col['Field'] === 'status') {
            $has_product_status = true;
        }
    }
    echo "</table>";
    
    // Check categories table structure
    echo "<h3>Categories Table:</h3>";
    $stmt = $pdo->query("DESCRIBE categories");
    $category_columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    $has_category_status = false;
    foreach ($category_columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
        if ($col['Field'] === 'status') {
            $has_category_status = true;
        }
    }
    echo "</table>";
    
    echo "<h2>🔧 Step 2: Fixing Missing Columns</h2>";
    
    // Fix products table
    if (!$has_product_status) {
        echo "<p style='color: orange;'>⚠️ Adding status column to products table...</p>";
        $pdo->exec("ALTER TABLE products ADD COLUMN status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active'");
        echo "<p style='color: green;'>✅ Status column added to products table</p>";
    } else {
        echo "<p style='color: green;'>✅ Products table already has status column</p>";
    }
    
    // Fix categories table
    if (!$has_category_status) {
        echo "<p style='color: orange;'>⚠️ Adding status column to categories table...</p>";
        $pdo->exec("ALTER TABLE categories ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'");
        echo "<p style='color: green;'>✅ Status column added to categories table</p>";
    } else {
        echo "<p style='color: green;'>✅ Categories table already has status column</p>";
    }
    
    echo "<h2>📊 Step 3: Updating Data</h2>";
    
    // Update all products to active status
    $stmt = $pdo->exec("UPDATE products SET status = 'active' WHERE status IS NULL OR status = ''");
    echo "<p style='color: green;'>✅ Updated $stmt products to active status</p>";
    
    // Update all categories to active status
    $stmt = $pdo->exec("UPDATE categories SET status = 'active' WHERE status IS NULL OR status = ''");
    echo "<p style='color: green;'>✅ Updated $stmt categories to active status</p>";
    
    echo "<h2>🧪 Step 4: Testing Database Queries</h2>";
    
    // Test basic product count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
    $total_products = $stmt->fetch()['total'];
    echo "<p style='color: blue;'>📊 Total products in database: $total_products</p>";
    
    // Test active products count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products WHERE status = 'active'");
    $active_products = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Active products: $active_products</p>";
    
    // Test categories count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM categories WHERE status = 'active'");
    $active_categories = $stmt->fetch()['total'];
    echo "<p style='color: green;'>✅ Active categories: $active_categories</p>";
    
    // Test the exact query from products.php
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name, c.slug as category_slug
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 10
    ");
    $test_products = $stmt->fetchAll();
    echo "<p style='color: green;'>✅ Products page query works: " . count($test_products) . " products found</p>";
    
    echo "<h2>🛍️ Step 5: Sample Products Preview</h2>";
    
    if (!empty($test_products)) {
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
        foreach (array_slice($test_products, 0, 6) as $product) {
            echo "<div style='border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f9f9f9;'>";
            echo "<h4 style='margin: 0 0 10px 0; color: #333;'>" . htmlspecialchars($product['name']) . "</h4>";
            echo "<p style='margin: 5px 0; color: #666;'>Category: " . htmlspecialchars($product['category_name'] ?? 'None') . "</p>";
            echo "<p style='margin: 5px 0; color: #666;'>Price: " . formatPrice($product['price']) . "</p>";
            echo "<p style='margin: 5px 0; color: #666;'>Stock: " . $product['stock_quantity'] . "</p>";
            echo "<p style='margin: 5px 0; color: #666;'>Status: " . $product['status'] . "</p>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    echo "<h2>🎯 Step 6: Final Verification</h2>";
    
    // Run the exact same query as products.php debug
    $debug_stmt = $pdo->query("SELECT COUNT(*) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.status = 'active'");
    $debug_total = $debug_stmt->fetch()['total'];
    
    if ($debug_total > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin: 0 0 15px 0;'>🎉 SUCCESS! Database is Fixed!</h3>";
        echo "<p style='color: #155724; margin: 0;'><strong>Found $debug_total active products</strong> - Your products page should now work perfectly!</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin: 0 0 15px 0;'>⚠️ No Products Found</h3>";
        echo "<p style='color: #721c24; margin: 0;'>The database structure is fixed, but no products were found. You may need to import products.</p>";
        echo "</div>";
    }
    
    echo "<h2>🚀 Next Steps</h2>";
    echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
    echo "<a href='products.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🛍️ Test Products Page</a>";
    echo "<a href='fix_column_error.php' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>📊 Import Products</a>";
    echo "<a href='test_complete_website.php' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧪 Full Test</a>";
    echo "<a href='index.php' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🏠 Home</a>";
    echo "</div>";
    
    // Show SQL commands for manual execution if needed
    echo "<h2>📋 Manual SQL Commands (if needed)</h2>";
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0;'>";
    echo "<p><strong>If you need to run these manually in phpMyAdmin:</strong></p>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo "-- Add status columns\n";
    echo "ALTER TABLE products ADD COLUMN status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active';\n";
    echo "ALTER TABLE categories ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active';\n\n";
    echo "-- Update existing data\n";
    echo "UPDATE products SET status = 'active';\n";
    echo "UPDATE categories SET status = 'active';\n\n";
    echo "-- Test query\n";
    echo "SELECT COUNT(*) FROM products WHERE status = 'active';";
    echo "</pre>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 15px 0;'>❌ Database Connection Error</h3>";
    echo "<p style='color: #721c24; margin: 0;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p style='color: #721c24; margin: 10px 0 0 0;'>Please ensure:</p>";
    echo "<ul style='color: #721c24;'>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>Database exists and is accessible</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

table {
    font-size: 12px;
}

th {
    background: #f8f9fa;
    padding: 8px;
    text-align: left;
}

td {
    padding: 6px 8px;
    border-bottom: 1px solid #eee;
}

pre {
    font-size: 12px;
    line-height: 1.4;
}

h1, h2, h3 {
    color: #333;
}
</style>
