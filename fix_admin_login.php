<?php
// Fix Admin Login Issue
require_once 'config/config.php';

echo "<h2>Admin Login Fix</h2>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: #f5f5f5;'>";

try {
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Check if admin_users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if (!$stmt->fetch()) {
        echo "<p style='color: red;'>❌ admin_users table does not exist. Creating it...</p>";
        
        // Create admin_users table
        $pdo->exec("
            CREATE TABLE admin_users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(100) UNIQUE NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                first_name VARCHAR(100) NOT NULL,
                last_name VARCHAR(100) NOT NULL,
                role ENUM('super_admin', 'admin', 'manager') DEFAULT 'admin',
                status ENUM('active', 'inactive') DEFAULT 'active',
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_role (role)
            )
        ");
        echo "<p style='color: green;'>✅ admin_users table created</p>";
    } else {
        echo "<p style='color: green;'>✅ admin_users table exists</p>";
    }
    
    // Check existing admin users
    $stmt = $pdo->query("SELECT * FROM admin_users");
    $existing_admins = $stmt->fetchAll();
    
    if (empty($existing_admins)) {
        echo "<p style='color: orange;'>⚠ No admin users found</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($existing_admins) . " admin user(s)</p>";
        echo "<table border='1' cellpadding='8' cellspacing='0' style='background: white; width: 100%;'>";
        echo "<tr style='background: #0d6efd; color: white;'>";
        echo "<th>ID</th><th>Username</th><th>Email</th><th>Role</th><th>Status</th><th>Created</th>";
        echo "</tr>";
        foreach ($existing_admins as $admin) {
            echo "<tr>";
            echo "<td>" . $admin['id'] . "</td>";
            echo "<td>" . $admin['username'] . "</td>";
            echo "<td>" . $admin['email'] . "</td>";
            echo "<td>" . $admin['role'] . "</td>";
            echo "<td>" . $admin['status'] . "</td>";
            echo "<td>" . $admin['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Create/Update admin user with correct password
    $username = 'admin';
    $email = '<EMAIL>';
    $password = 'admin123';
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    echo "<h3>Creating/Updating Admin User:</h3>";
    
    // Delete existing admin user if exists
    $stmt = $pdo->prepare("DELETE FROM admin_users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);
    
    // Create new admin user
    $stmt = $pdo->prepare("
        INSERT INTO admin_users (username, email, password, first_name, last_name, role, status) 
        VALUES (?, ?, ?, 'Admin', 'User', 'super_admin', 'active')
    ");
    $result = $stmt->execute([$username, $email, $hashed_password]);
    
    if ($result) {
        echo "<p style='color: green;'>✅ Admin user created successfully!</p>";
        
        // Verify the password works
        $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ?");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($password, $admin['password'])) {
            echo "<p style='color: green;'>✅ Password verification successful!</p>";
        } else {
            echo "<p style='color: red;'>❌ Password verification failed!</p>";
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>Login Credentials:</h3>";
        echo "<strong>Username:</strong> admin<br>";
        echo "<strong>Password:</strong> admin123<br>";
        echo "<strong>Login URL:</strong> <a href='admin/login.php' style='color: #0d6efd;'>Admin Login</a>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create admin user!</p>";
    }
    
    // Test the login process
    echo "<h3>Testing Login Process:</h3>";
    
    $stmt = $pdo->prepare("
        SELECT id, username, password, first_name, last_name, role, status 
        FROM admin_users 
        WHERE (username = ? OR email = ?) AND status = 'active'
    ");
    $stmt->execute([$username, $username]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin user found in database</p>";
        
        if (password_verify($password, $admin['password'])) {
            echo "<p style='color: green;'>✅ Password verification works correctly</p>";
            echo "<p style='color: green;'>🎉 Admin login should work now!</p>";
        } else {
            echo "<p style='color: red;'>❌ Password verification failed</p>";
            echo "<p>Stored hash: " . substr($admin['password'], 0, 20) . "...</p>";
            echo "<p>Testing password: " . $password . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Admin user not found or inactive</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>Database connection settings in config/database.php</li>";
    echo "<li>MySQL service is running</li>";
    echo "<li>Database exists</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "</div>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 0;
    background-color: #f8f9fa;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #0d6efd;
    color: white;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

a {
    color: #0d6efd;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
