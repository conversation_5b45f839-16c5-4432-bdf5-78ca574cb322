<?php
require_once 'config/config.php';

echo "<h1>🔥 TeWuNeed Complete System Test</h1>";

echo "<h2>1. Authentication Status</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✓ User is logged in</p>";
    echo "<p>User ID: " . $_SESSION['user_id'] . "</p>";
    echo "<p>User Email: " . ($_SESSION['user_email'] ?? 'Not set') . "</p>";
    echo "<p>User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "</p>";
    
    // Test my-orders page
    echo "<h2>2. My Orders Test</h2>";
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            SELECT o.*, COUNT(oi.id) as item_count
            FROM orders o 
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE o.user_id = ?
            GROUP BY o.id
            ORDER BY o.created_at DESC
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $user_orders = $stmt->fetchAll();
        
        echo "<p>✓ Orders query successful</p>";
        echo "<p>Found <strong>" . count($user_orders) . "</strong> orders for this user</p>";
        
        if (count($user_orders) > 0) {
            echo "<p style='color: green;'>✓ My Orders page should work properly</p>";
        } else {
            echo "<p style='color: orange;'>⚠ No orders found - My Orders page will show empty state</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Orders query failed: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ User is NOT logged in</p>";
    echo "<p>You need to login first to test the orders page.</p>";
}

echo "<h2>3. Database Status</h2>";
try {
    $pdo = getDBConnection();
    
    // Check orders
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $order_count = $stmt->fetch()['count'];
    echo "<p>Total orders in database: <strong>$order_count</strong></p>";
    
    // Check users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $user_count = $stmt->fetch()['count'];
    echo "<p>Total users in database: <strong>$user_count</strong></p>";
    
    // Check products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $product_count = $stmt->fetch()['count'];
    echo "<p>Total products in database: <strong>$product_count</strong></p>";
    
    echo "<p style='color: green;'>✓ Database is properly set up</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Quick Navigation</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";

if (!isset($_SESSION['user_id'])) {
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔑 Login</a>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📝 Register</a>";
} else {
    echo "<a href='my-orders.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📦 My Orders</a>";
    echo "<a href='logout.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚪 Logout</a>";
}

echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Homepage</a>";
echo "<a href='products.php' style='background: #fd7e14; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛍️ Products</a>";
echo "<a href='order-review.php' style='background: #20c997; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🛒 Place Order</a>";
echo "<a href='admin/orders.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⚙️ Admin Orders</a>";
echo "</div>";

echo "<h2>5. System Status Summary</h2>";
$all_good = true;

if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: orange;'>⚠ Not logged in - Login to test full functionality</p>";
    $all_good = false;
}

if ($order_count == 0) {
    echo "<p style='color: orange;'>⚠ No orders in database - Place a test order</p>";
    $all_good = false;
}

if ($all_good && isset($_SESSION['user_id'])) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>🎉 System is fully functional!</p>";
    echo "<p>✅ Authentication working</p>";
    echo "<p>✅ Database connected</p>";
    echo "<p>✅ Orders system ready</p>";
    echo "<p>✅ My Orders page should work</p>";
}

echo "<hr>";
echo "<h2>🔥 Firebase Authentication Test</h2>";
echo "<div id='firebase-status'></div>";
?>

<script type="module">
// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import {
    getAuth,
    onAuthStateChanged
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDz5t6mlBzXq7bjK3PGOGyEBo_WzjsHYME",
    authDomain: "tewuneed-marketplace.firebaseapp.com",
    databaseURL: "https://tewuneed-marketplace-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "tewuneed-marketplace",
    storageBucket: "tewuneed-marketplace.firebasestorage.app",
    messagingSenderId: "************",
    appId: "1:************:web:87b68aa3a5a5ebca395893",
    measurementId: "G-8WNLD8T7GY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Check Firebase auth state
onAuthStateChanged(auth, (user) => {
    const statusDiv = document.getElementById('firebase-status');
    if (user) {
        statusDiv.innerHTML = `
            <p style='color: green;'>✓ Firebase user is logged in</p>
            <p>Firebase UID: ${user.uid}</p>
            <p>Email: ${user.email}</p>
            <p>Display Name: ${user.displayName || 'Not set'}</p>
            <p style='color: green; font-weight: bold;'>🔥 Firebase authentication is working!</p>
        `;
    } else {
        statusDiv.innerHTML = `
            <p style='color: red;'>✗ No Firebase user logged in</p>
            <p>Please login through the Firebase login system.</p>
        `;
    }
});
</script>
