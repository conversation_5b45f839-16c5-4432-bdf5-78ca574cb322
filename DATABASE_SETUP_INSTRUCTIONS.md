# TeWuNeed Database Setup Instructions

## Quick Setup for SQLyog

1. **Open SQLyog**
2. **Connect to your MySQL server** (localhost, root, no password)
3. **Run the SQL script**:
   - Open the file `database_setup.sql`
   - Execute the entire script
   - This will create the `db_tewuneed` database with all tables and sample data

## What the Script Does

✅ Creates `db_tewuneed` database  
✅ Creates all required tables with proper structure  
✅ Inserts sample products, categories, and users  
✅ Sets up admin account  
✅ Adds test data for orders, reviews, etc.  

## Test Accounts After Setup

### User Accounts (Password: user123)
- <EMAIL>
- <EMAIL>  
- <EMAIL>

### Admin Account (Password: admin123)
- Username: admin
- Email: <EMAIL>

## After Running the Script

1. **Test Login**: Go to http://localhost/tewuneed2/login.php
2. **Use any test account** listed above
3. **<PERSON><PERSON> should work perfectly** and redirect to home page
4. **Delete this instruction file** when done

## Database Structure Created

- `users` - User accounts and profiles
- `categories` - Product categories  
- `products` - Product catalog
- `admin_users` - Admin accounts
- `orders` & `order_items` - Order management
- `cart` & `cart_items` - Shopping cart
- `product_reviews` - Product reviews
- `wishlist` - User wishlists
- `notifications` - User notifications
- `coupons` - Discount coupons
- `settings` - Website settings
- And more supporting tables...

## Troubleshooting

If you get any errors:
1. Make sure MySQL is running
2. Check that you have proper permissions
3. Ensure no other database with same name exists
4. Run the script step by step if needed

Your login system will be fully functional after running this script!
