<?php
require_once 'config/config.php';

// Auto-login for testing (bypass normal login)
if (!isset($_SESSION['user_id'])) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['user_email'] = $user['email'];
        }
    } catch (Exception $e) {
        // Handle error silently
    }
}

$page_title = 'My Orders - Quick Access';
$page_description = 'Quick access to My Orders with auto-login';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>My Orders - Access Error</title>
        <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    </head>
    <body>
        <div class='container py-5'>
            <div class='alert alert-danger'>
                <h4>❌ Cannot Access My Orders</h4>
                <p>Unable to auto-login with test account. Please check:</p>
                <ul>
                    <li>Database connection</li>
                    <li>Test user exists</li>
                    <li>Session configuration</li>
                </ul>
                <a href='fix-my-orders.php' class='btn btn-warning'>Fix Database</a>
                <a href='test-login-and-orders.php' class='btn btn-primary'>Manual Login</a>
            </div>
        </div>
    </body>
    </html>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Get orders
$orders = [];
$error_message = '';

try {
    $pdo = getDBConnection();
    
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as item_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.user_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user_id]);
    $orders = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = 'Error loading orders: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-success">✅ My Orders - Quick Access</h1>
        <p class="lead text-gray-600">Auto-logged in as test user - viewing your orders</p>
    </div>

    <!-- Success Message -->
    <div class="alert alert-success">
        <h5 class="alert-heading">
            <i class="fas fa-check-circle me-2"></i>Access Successful!
        </h5>
        <p class="mb-2">Successfully logged in as: <strong><?php echo htmlspecialchars($_SESSION['user_name']); ?></strong></p>
        <p class="mb-0">User ID: <strong><?php echo $_SESSION['user_id']; ?></strong> | Email: <strong><?php echo htmlspecialchars($_SESSION['user_email']); ?></strong></p>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="h3 mb-1 text-primary">
                <i class="fas fa-shopping-bag me-2"></i>My Orders
            </h2>
            <p class="text-muted">Track and manage your order history</p>
        </div>
        <div class="text-end">
            <span class="badge bg-primary fs-6 mb-2"><?php echo count($orders); ?> Orders</span>
            <br>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-plus me-1"></i>Shop More
            </a>
        </div>
    </div>

    <!-- Orders List -->
    <?php if (empty($orders)): ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                        <i class="fas fa-shopping-bag fa-3x text-muted"></i>
                    </div>
                </div>
                <h3 class="text-gray-900 mb-3">No Orders Found</h3>
                <p class="text-muted mb-4 lead">
                    You haven't placed any orders yet. Start shopping to see your orders here!
                </p>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                    </a>
                    <a href="<?php echo SITE_URL; ?>/fix-my-orders.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-wrench me-2"></i>Create Sample Orders
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($orders as $order): ?>
                <?php
                $status_colors = [
                    'pending' => 'warning',
                    'processing' => 'info',
                    'shipped' => 'primary',
                    'delivered' => 'success',
                    'cancelled' => 'danger'
                ];
                
                $payment_colors = [
                    'pending' => 'warning',
                    'paid' => 'success',
                    'failed' => 'danger',
                    'refunded' => 'secondary'
                ];
                
                $status_color = $status_colors[$order['status']] ?? 'secondary';
                $payment_color = $payment_colors[$order['payment_status']] ?? 'secondary';
                ?>
                <div class="col-lg-6 mb-4">
                    <div class="card h-100 shadow-sm border-0" style="transition: all 0.3s ease;">
                        <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 text-primary fw-bold">
                                    <i class="fas fa-receipt me-2"></i>
                                    <?php echo htmlspecialchars($order['order_number']); ?>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo formatDate($order['created_at']); ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo $status_color; ?> mb-1 px-3 py-2">
                                    <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                                <br>
                                <span class="badge bg-<?php echo $payment_color; ?> px-3 py-1">
                                    <i class="fas fa-credit-card me-1"></i>
                                    <?php echo ucfirst($order['payment_status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Items</small>
                                    <div class="fw-bold"><?php echo $order['item_count']; ?> item(s)</div>
                                </div>
                                <div class="col-6 text-end">
                                    <small class="text-muted">Total</small>
                                    <div class="fw-bold text-success"><?php echo formatPrice($order['total_amount']); ?></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">Products</small>
                                <div class="small">
                                    <?php 
                                    $product_names = $order['product_names'] ?? 'No products';
                                    echo strlen($product_names) > 60 ? substr($product_names, 0, 60) . '...' : $product_names;
                                    ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($order['payment_method'])): ?>
                            <div class="mb-3">
                                <small class="text-muted">Payment Method</small>
                                <div class="small"><?php echo htmlspecialchars($order['payment_method']); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo timeAgo($order['created_at']); ?>
                                </small>
                                <div>
                                    <a href="order-confirmation.php?order=<?php echo $order['order_number']; ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <?php if ($order['status'] === 'pending'): ?>
                                        <button class="btn btn-sm btn-outline-danger ms-1" 
                                                onclick="alert('Cancel order feature coming soon!')">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="text-center mt-5">
        <h4 class="mb-4">Quick Actions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/my-orders.php" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Regular My Orders Page
            </a>
            <a href="<?php echo SITE_URL; ?>/my-orders-debug.php" class="btn btn-info">
                <i class="fas fa-bug me-2"></i>Debug Version
            </a>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-success">
                <i class="fas fa-shopping-cart me-2"></i>Shop Products
            </a>
            <a href="<?php echo SITE_URL; ?>/logout.php" class="btn btn-outline-secondary">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>
</div>

<style>
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}
</style>

<?php include 'includes/footer.php'; ?>
