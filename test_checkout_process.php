<?php
// Test Complete Checkout Process
require_once 'config/config.php';

echo "<h1>🧪 Test Complete Checkout Process</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px;'>";

try {
    $pdo = getDBConnection();
    
    echo "<h2>Step 1: Clear Previous Test Data</h2>";
    
    // Clear any existing test cart items
    $session_id = session_id();
    $pdo->exec("DELETE FROM cart WHERE session_id = '$session_id' AND user_id IS NULL");
    echo "<p style='color: green;'>✓ Cleared test cart data</p>";
    
    echo "<h2>Step 2: Add Test Products to Cart</h2>";
    
    // Get some test products
    $stmt = $pdo->query("SELECT id, name, price FROM products WHERE status = 'active' LIMIT 3");
    $test_products = $stmt->fetchAll();
    
    if (empty($test_products)) {
        echo "<p style='color: red;'>❌ No active products found. Please add some products first.</p>";
        exit;
    }
    
    // Add products to cart
    foreach ($test_products as $product) {
        $stmt = $pdo->prepare("
            INSERT INTO cart (session_id, product_id, quantity, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$session_id, $product['id'], 1]);
        echo "<p>• Added {$product['name']} to cart</p>";
    }
    
    echo "<p style='color: green;'>✓ Test products added to cart</p>";
    
    echo "<h2>Step 3: Verify Cart Contents</h2>";
    
    $stmt = $pdo->prepare("
        SELECT c.*, p.name, p.price, p.sale_price 
        FROM cart c 
        JOIN products p ON c.product_id = p.id 
        WHERE c.session_id = ? AND c.user_id IS NULL
    ");
    $stmt->execute([$session_id]);
    $cart_items = $stmt->fetchAll();
    
    $subtotal = 0;
    foreach ($cart_items as $item) {
        $price = $item['sale_price'] ?: $item['price'];
        $subtotal += $price * $item['quantity'];
        echo "<p>• {$item['name']} - Qty: {$item['quantity']} - Price: " . formatPrice($price) . "</p>";
    }
    
    echo "<p><strong>Subtotal: " . formatPrice($subtotal) . "</strong></p>";
    echo "<p style='color: green;'>✓ Cart verification complete</p>";
    
    echo "<h2>Step 4: Test Checkout Form Access</h2>";
    
    // Check if checkout.php is accessible
    $checkout_url = SITE_URL . '/checkout.php';
    echo "<p>Checkout URL: <a href='$checkout_url' target='_blank'>$checkout_url</a></p>";
    echo "<p style='color: green;'>✓ Checkout form should be accessible</p>";
    
    echo "<h2>Step 5: Test Order Review Process</h2>";
    
    // Simulate checkout data
    $_SESSION['checkout_data'] = [
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'phone' => '************',
        'address_line_1' => 'Jl. Test No. 123',
        'address_line_2' => '',
        'city' => 'Jakarta',
        'state' => 'DKI Jakarta',
        'postal_code' => '12345',
        'country' => 'Indonesia',
        'payment_method' => 'Bank Transfer BCA',
        'notes' => 'Test order',
        'coupon_code' => '',
        'discount_amount' => 0,
        'coupon_id' => null
    ];
    
    $order_review_url = SITE_URL . '/order-review.php';
    echo "<p>Order Review URL: <a href='$order_review_url' target='_blank'>$order_review_url</a></p>";
    echo "<p style='color: green;'>✓ Order review should be accessible</p>";
    
    echo "<h2>Step 6: Database Table Verification</h2>";
    
    // Check if all required tables exist
    $required_tables = ['orders', 'order_items', 'cart', 'products', 'coupons'];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
        }
    }
    
    echo "<h2>✅ Checkout Process Test Complete!</h2>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li><a href='$checkout_url' target='_blank'>Go to Checkout Form</a></li>";
    echo "<li>Fill in the customer information</li>";
    echo "<li>Proceed to Order Review</li>";
    echo "<li>Confirm the order</li>";
    echo "<li>Check Order Confirmation page</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}

echo "</div>";
?>
