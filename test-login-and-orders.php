<?php
require_once 'config/config.php';

$page_title = 'Test Login & Orders';
$page_description = 'Test login functionality and access My Orders';

// Handle login form submission
$login_error = '';
$login_success = false;

if ($_POST && isset($_POST['email']) && isset($_POST['password'])) {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // Login successful
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['user_email'] = $user['email'];
            $login_success = true;
        } else {
            $login_error = 'Invalid email or password';
        }
    } catch (Exception $e) {
        $login_error = 'Login error: ' . $e->getMessage();
    }
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">🔐 Test Login & My Orders</h1>
        <p class="lead text-gray-600">Login with test account and access My Orders page</p>
    </div>

    <!-- Current Session Status -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0">📊 Current Session Status</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Session Information</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <strong>Session ID:</strong> 
                            <code><?php echo session_id(); ?></code>
                        </li>
                        <li class="mb-2">
                            <strong>User ID:</strong> 
                            <?php if (isset($_SESSION['user_id'])): ?>
                                <span class="badge bg-success"><?php echo $_SESSION['user_id']; ?></span>
                            <?php else: ?>
                                <span class="badge bg-danger">Not Set</span>
                            <?php endif; ?>
                        </li>
                        <li class="mb-2">
                            <strong>User Name:</strong> 
                            <?php if (isset($_SESSION['user_name'])): ?>
                                <span class="text-success"><?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                            <?php else: ?>
                                <span class="text-danger">Not Set</span>
                            <?php endif; ?>
                        </li>
                        <li class="mb-2">
                            <strong>User Email:</strong> 
                            <?php if (isset($_SESSION['user_email'])): ?>
                                <span class="text-success"><?php echo htmlspecialchars($_SESSION['user_email']); ?></span>
                            <?php else: ?>
                                <span class="text-danger">Not Set</span>
                            <?php endif; ?>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Login Status</h6>
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Logged In Successfully!</strong><br>
                            You can now access the My Orders page.
                        </div>
                        <div class="d-grid gap-2">
                            <a href="<?php echo SITE_URL; ?>/my-orders.php" class="btn btn-primary">
                                <i class="fas fa-shopping-bag me-2"></i>Go to My Orders
                            </a>
                            <a href="<?php echo SITE_URL; ?>/logout.php" class="btn btn-outline-secondary">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Not Logged In</strong><br>
                            Please login to access My Orders page.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php if (!isset($_SESSION['user_id'])): ?>
    <!-- Login Form -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">🔑 Quick Login</h4>
        </div>
        <div class="card-body">
            <?php if ($login_error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <?php echo htmlspecialchars($login_error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($login_success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    Login successful! Redirecting to My Orders...
                </div>
                <script>
                    setTimeout(function() {
                        window.location.href = '<?php echo SITE_URL; ?>/my-orders.php';
                    }, 2000);
                </script>
            <?php else: ?>
                <form method="POST" class="row g-3">
                    <div class="col-md-6">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<EMAIL>" required>
                        <div class="form-text">Pre-filled with test account email</div>
                    </div>
                    <div class="col-md-6">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" 
                               value="password123" required>
                        <div class="form-text">Pre-filled with test account password</div>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>Login & Test My Orders
                        </button>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Test Credentials -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">🎯 Test Credentials</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Test Account</h6>
                    <div class="bg-light p-3 rounded">
                        <p class="mb-2"><strong>Email:</strong> <code><EMAIL></code></p>
                        <p class="mb-0"><strong>Password:</strong> <code>password123</code></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Expected Results</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Login should work immediately
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Session variables should be set
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            My Orders page should be accessible
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            Should see 3 sample orders
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Check -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-warning text-dark">
            <h4 class="mb-0">🔍 Database Check</h4>
        </div>
        <div class="card-body">
            <?php
            try {
                $pdo = getDBConnection();
                
                // Check users
                $stmt = $pdo->query("SELECT COUNT(*) FROM users");
                $user_count = $stmt->fetchColumn();
                
                // Check orders
                $stmt = $pdo->query("SELECT COUNT(*) FROM orders");
                $order_count = $stmt->fetchColumn();
                
                // Check test user
                $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
                $stmt->execute(['<EMAIL>']);
                $test_user = $stmt->fetch();
                
                echo "<div class='row'>";
                echo "<div class='col-md-4'>";
                echo "<h6 class='fw-bold mb-3'>Database Status</h6>";
                echo "<ul class='list-unstyled'>";
                echo "<li class='mb-2'><i class='fas fa-users text-primary me-2'></i>Users: <strong>$user_count</strong></li>";
                echo "<li class='mb-2'><i class='fas fa-shopping-cart text-primary me-2'></i>Orders: <strong>$order_count</strong></li>";
                echo "</ul>";
                echo "</div>";
                
                echo "<div class='col-md-8'>";
                echo "<h6 class='fw-bold mb-3'>Test User Status</h6>";
                if ($test_user) {
                    echo "<div class='alert alert-success'>";
                    echo "<i class='fas fa-check-circle me-2'></i>";
                    echo "<strong>Test user exists!</strong><br>";
                    echo "ID: {$test_user['id']}, Name: {$test_user['first_name']} {$test_user['last_name']}, Status: {$test_user['status']}";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-danger'>";
                    echo "<i class='fas fa-exclamation-circle me-2'></i>";
                    echo "<strong>Test user not found!</strong> Please run the database fix script first.";
                    echo "</div>";
                }
                echo "</div>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>";
                echo "<i class='fas fa-exclamation-circle me-2'></i>";
                echo "<strong>Database Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
            ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="text-center">
        <h4 class="mb-4">Quick Actions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <?php if (!isset($_SESSION['user_id'])): ?>
                <a href="<?php echo SITE_URL; ?>/fix-my-orders.php" class="btn btn-warning">
                    <i class="fas fa-wrench me-2"></i>Fix Database First
                </a>
                <a href="<?php echo SITE_URL; ?>/login.php" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Regular Login Page
                </a>
            <?php else: ?>
                <a href="<?php echo SITE_URL; ?>/my-orders.php" class="btn btn-success btn-lg">
                    <i class="fas fa-shopping-bag me-2"></i>Go to My Orders
                </a>
                <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-info">
                    <i class="fas fa-shopping-cart me-2"></i>Shop Products
                </a>
                <a href="<?php echo SITE_URL; ?>/logout.php" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
