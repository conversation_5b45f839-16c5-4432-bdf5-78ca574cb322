<?php
require_once 'config/config.php';

$page_title = 'My Wishlist';
$page_description = 'View and manage your favorite products';

// Check if user is logged in - with fallback for <PERSON> account
if (!isset($_SESSION['user_id'])) {
    // Try to find and auto-login Amos Baringbing account
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email LIKE ? OR first_name LIKE ? AND status = 'active'");
        $stmt->execute(['%amos%', '%amos%']);
        $user = $stmt->fetch();

        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['user_email'] = $user['email'];
        }
    } catch (Exception $e) {
        // Handle error silently
    }

    // If still not logged in, redirect
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        $_SESSION['error'] = 'Please login to view your wishlist.';

        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Redirecting to Login...</title>
            <meta http-equiv='refresh' content='0;url=" . SITE_URL . "/login.php'>
        </head>
        <body>
            <script>
                window.location.href = '" . SITE_URL . "/login.php';
            </script>
            <p>Redirecting to login page... <a href='" . SITE_URL . "/login.php'>Click here if not redirected automatically</a></p>
        </body>
        </html>";
        exit;
    }
}

$user_id = $_SESSION['user_id'];

// Handle remove from wishlist
if (isset($_POST['remove_item'])) {
    $product_id = (int)$_POST['product_id'];
    
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("DELETE FROM wishlist WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$user_id, $product_id]);
        
        $_SESSION['success'] = 'Item removed from wishlist!';
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    } catch (Exception $e) {
        $_SESSION['error'] = 'Error removing item from wishlist.';
    }
}

// Get wishlist items
$wishlist_items = [];
$error_message = '';

try {
    $pdo = getDBConnection();
    
    $sql = "
        SELECT w.*, p.name, p.price, p.sale_price, p.image, p.slug, p.stock_quantity,
               p.short_description, c.name as category_name
        FROM wishlist w
        JOIN products p ON w.product_id = p.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE w.user_id = ? AND p.status = 'active'
        ORDER BY w.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user_id]);
    $wishlist_items = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = 'Error loading wishlist: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <!-- Page Header -->
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold text-primary">
            <i class="fas fa-heart me-3"></i>My Wishlist
        </h1>
        <p class="lead text-muted">Your favorite products saved for later</p>
    </div>

    <!-- Messages -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Wishlist Stats -->
    <div class="row g-4 mb-5">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-heart fa-lg text-white"></i>
                    </div>
                    <h4 class="fw-bold text-primary"><?php echo count($wishlist_items); ?></h4>
                    <p class="text-muted mb-0">Items in Wishlist</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="bg-success rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-tag fa-lg text-white"></i>
                    </div>
                    <h4 class="fw-bold text-success">
                        <?php 
                        $total_value = 0;
                        foreach ($wishlist_items as $item) {
                            $price = $item['sale_price'] ?? $item['price'];
                            $total_value += $price;
                        }
                        echo formatPrice($total_value);
                        ?>
                    </h4>
                    <p class="text-muted mb-0">Total Value</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="bg-warning rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-star fa-lg text-white"></i>
                    </div>
                    <h4 class="fw-bold text-warning">
                        <?php 
                        $available_items = 0;
                        foreach ($wishlist_items as $item) {
                            if ($item['stock_quantity'] > 0) $available_items++;
                        }
                        echo $available_items;
                        ?>
                    </h4>
                    <p class="text-muted mb-0">Available Items</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Wishlist Items -->
    <?php if (empty($wishlist_items)): ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                        <i class="fas fa-heart fa-3x text-muted"></i>
                    </div>
                </div>
                <h3 class="text-gray-900 mb-3">Your Wishlist is Empty</h3>
                <p class="text-muted mb-4 lead">
                    Start adding products you love to your wishlist and keep track of items you want to buy later!
                </p>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>Browse Products
                    </a>
                    <a href="<?php echo SITE_URL; ?>/setup-wishlist.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-plus me-2"></i>Add Sample Items
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($wishlist_items as $item): ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card h-100 shadow-sm border-0 wishlist-card">
                        <div class="position-relative">
                            <?php if ($item['image']): ?>
                                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $item['image']; ?>" 
                                     class="card-img-top" alt="<?php echo htmlspecialchars($item['name']); ?>"
                                     style="height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Stock Status -->
                            <?php if ($item['stock_quantity'] <= 0): ?>
                                <span class="position-absolute top-0 end-0 badge bg-danger m-2">Out of Stock</span>
                            <?php elseif ($item['stock_quantity'] <= 5): ?>
                                <span class="position-absolute top-0 end-0 badge bg-warning m-2">Low Stock</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <?php if ($item['category_name']): ?>
                                    <span class="badge bg-light text-dark small"><?php echo htmlspecialchars($item['category_name']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <h5 class="card-title fw-bold mb-2">
                                <a href="<?php echo SITE_URL; ?>/product-detail.php?slug=<?php echo $item['slug']; ?>" 
                                   class="text-decoration-none text-dark">
                                    <?php echo htmlspecialchars($item['name']); ?>
                                </a>
                            </h5>
                            
                            <?php if ($item['short_description']): ?>
                                <p class="card-text text-muted small mb-3">
                                    <?php echo htmlspecialchars(substr($item['short_description'], 0, 100)) . '...'; ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <?php if ($item['sale_price']): ?>
                                    <span class="h5 fw-bold text-danger me-2"><?php echo formatPrice($item['sale_price']); ?></span>
                                    <span class="text-muted text-decoration-line-through"><?php echo formatPrice($item['price']); ?></span>
                                    <span class="badge bg-danger ms-2">
                                        <?php echo round((($item['price'] - $item['sale_price']) / $item['price']) * 100); ?>% OFF
                                    </span>
                                <?php else: ?>
                                    <span class="h5 fw-bold text-primary"><?php echo formatPrice($item['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mt-auto">
                                <small class="text-muted d-block mb-3">
                                    <i class="fas fa-clock me-1"></i>
                                    Added <?php echo timeAgo($item['created_at']); ?>
                                </small>
                                
                                <div class="d-flex gap-2">
                                    <?php if ($item['stock_quantity'] > 0): ?>
                                        <a href="<?php echo SITE_URL; ?>/product-detail.php?slug=<?php echo $item['slug']; ?>" 
                                           class="btn btn-primary flex-fill">
                                            <i class="fas fa-shopping-cart me-1"></i>Add to Cart
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-secondary flex-fill" disabled>
                                            <i class="fas fa-times me-1"></i>Out of Stock
                                        </button>
                                    <?php endif; ?>
                                    
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                        <button type="submit" name="remove_item" class="btn btn-outline-danger" 
                                                onclick="return confirm('Remove this item from wishlist?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Bulk Actions -->
        <div class="text-center mt-5">
            <h4 class="mb-4">Wishlist Actions</h4>
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>Add More Items
                </a>
                <a href="<?php echo SITE_URL; ?>/cart.php" class="btn btn-success btn-lg">
                    <i class="fas fa-shopping-cart me-2"></i>View Cart
                </a>
                <button class="btn btn-outline-danger btn-lg" onclick="alert('Clear all feature coming soon!')">
                    <i class="fas fa-trash me-2"></i>Clear All
                </button>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.wishlist-card {
    transition: all 0.3s ease;
}

.wishlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.text-gray-900 {
    color: #1a202c !important;
}
</style>

<?php include 'includes/footer.php'; ?>
