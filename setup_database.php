<?php
// Database Setup Script for TeWuNeed
// Run this file once to set up the database

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'db_tewuneed';

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>TeWuNeed Database Setup</h2>";
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;'>";
    
    // Create database
    echo "<p>Creating database '$database'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    echo "<p style='color: green;'>✓ Database created successfully!</p>";
    
    // Use the database
    $pdo->exec("USE $database");
    
    // Create tables
    echo "<p>Creating tables...</p>";
    
    // Categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            image VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✓ Categories table created!</p>";
    
    // Products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            short_description VARCHAR(500),
            price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NULL,
            sku VARCHAR(100) UNIQUE,
            stock_quantity INT DEFAULT 0,
            category_id INT,
            image VARCHAR(255),
            gallery TEXT,
            status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active',
            featured BOOLEAN DEFAULT FALSE,
            weight DECIMAL(8,2) DEFAULT 0,
            dimensions VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
            INDEX idx_category (category_id),
            INDEX idx_status (status),
            INDEX idx_featured (featured)
        )
    ");
    echo "<p style='color: green;'>✓ Products table created!</p>";
    
    // Users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            date_of_birth DATE,
            gender ENUM('male', 'female', 'other'),
            avatar VARCHAR(255),
            email_verified BOOLEAN DEFAULT FALSE,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_email (email),
            INDEX idx_status (status)
        )
    ");
    echo "<p style='color: green;'>✓ Users table created!</p>";
    
    // Cart table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cart (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT,
            session_id VARCHAR(255),
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_user (user_id),
            INDEX idx_session (session_id),
            INDEX idx_product (product_id)
        )
    ");
    echo "<p style='color: green;'>✓ Cart table created!</p>";
    
    // Admin users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(100) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            role ENUM('super_admin', 'admin', 'manager') DEFAULT 'admin',
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_email (email),
            INDEX idx_role (role)
        )
    ");
    echo "<p style='color: green;'>✓ Admin users table created!</p>";
    
    // Insert sample data
    echo "<p>Inserting sample data...</p>";
    
    // Insert categories
    $categories = [
        ['Electronics', 'electronics', 'Electronic devices and gadgets'],
        ['Cosmetics', 'cosmetics', 'Beauty and cosmetic products'],
        ['Sports', 'sports', 'Sports and fitness equipment'],
        ['Food & Snacks', 'food-snacks', 'Food items and snacks'],
        ['Health & Medicine', 'health-medicine', 'Health and medical products'],
        ['Vegetables', 'vegetables', 'Fresh vegetables and produce']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, slug, description) VALUES (?, ?, ?)");
    foreach ($categories as $category) {
        $stmt->execute($category);
    }
    echo "<p style='color: green;'>✓ Sample categories inserted!</p>";
    
    // Insert sample products
    $products = [
        ['iPhone 14 Pro', 'iphone-14-pro', 'Latest iPhone with advanced features', 'Premium smartphone with excellent camera', 15000000, 14000000, 'IP14PRO001', 10, 1, 'iPhone 14 Best Seller Edition.jpg', 1],
        ['Samsung Galaxy S23', 'samsung-galaxy-s23', 'Flagship Android smartphone', 'High-performance Android device', 12000000, null, 'SGS23001', 15, 1, 'default-product.jpg', 1],
        ['Serum Vitamin C 20%', 'serum-vitamin-c-20', 'Anti-aging vitamin C serum', 'Brightening and anti-aging serum', 250000, 200000, 'SVC20001', 50, 2, 'Serum Vitamin C 20%.jpg', 1],
        ['Lipstick Matte Red', 'lipstick-matte-red', 'Long-lasting matte lipstick', 'Bold red matte finish lipstick', 150000, null, 'LMR001', 30, 2, 'Lipstick Matte Red.jpeg', 0],
        ['Dumbbell Set 20kg', 'dumbbell-set-20kg', 'Professional dumbbell set', 'Adjustable weight dumbbell set', 800000, 750000, 'DS20001', 5, 3, 'drumbel.jpg', 1],
        ['Yoga Mat Premium', 'yoga-mat-premium', 'High-quality yoga mat', 'Non-slip premium yoga mat', 300000, null, 'YMP001', 20, 3, 'matras yoga.jpg', 0]
    ];
    
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO products (name, slug, description, short_description, price, sale_price, sku, stock_quantity, category_id, image, featured) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    foreach ($products as $product) {
        $stmt->execute($product);
    }
    echo "<p style='color: green;'>✓ Sample products inserted!</p>";
    
    // Insert admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO admin_users (username, email, password, first_name, last_name, role) 
        VALUES ('admin', '<EMAIL>', ?, 'Admin', 'User', 'super_admin')
    ");
    $stmt->execute([$admin_password]);
    echo "<p style='color: green;'>✓ Admin user created! (Username: admin, Password: admin123)</p>";
    
    echo "<h3 style='color: green;'>🎉 Database setup completed successfully!</h3>";
    echo "<p><strong>Your website is now ready to preview!</strong></p>";
    echo "<p>You can access your website at: <a href='http://localhost/tewuneed2/' target='_blank'>http://localhost/tewuneed2/</a></p>";
    echo "<p>Admin panel will be available at: <a href='http://localhost/tewuneed2/admin/' target='_blank'>http://localhost/tewuneed2/admin/</a></p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='color: red; font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;'>";
    echo "<h3>Database Setup Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please make sure:</p>";
    echo "<ul>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>Database credentials are correct</li>";
    echo "</ul>";
    echo "</div>";
}
?>
