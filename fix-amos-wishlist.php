<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Amos Wishlist Access</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔧 Fix Amos Wishlist Access</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='alert alert-info'>";
    echo "<h5><i class='fas fa-info-circle me-2'></i>Fixing Wishlist Access for <PERSON>ngbing</h5>";
    echo "<p>You're logged in as <PERSON> but the wishlist page can't access your session. Let me fix this.</p>";
    echo "</div>";
    
    // Find Amos account
    $search_terms = [
        "<EMAIL>",
        "%amos%",
        "%baringbing%"
    ];
    
    $found_user = null;
    foreach ($search_terms as $term) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email LIKE ? OR first_name LIKE ? OR last_name LIKE ?");
        $stmt->execute([$term, $term, $term]);
        $users = $stmt->fetchAll();
        
        if (!empty($users)) {
            foreach ($users as $user) {
                if (stripos($user['email'], 'amos') !== false || 
                    stripos($user['first_name'], 'amos') !== false ||
                    stripos($user['last_name'], 'baringbing') !== false) {
                    $found_user = $user;
                    break 2;
                }
            }
        }
    }
    
    if (!$found_user) {
        echo "<div class='alert alert-warning'>";
        echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Amos Account Not Found</h6>";
        echo "<p>Creating Amos Baringbing account...</p>";
        
        $password = password_hash('amos123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, first_name, last_name, phone, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            '<EMAIL>',
            $password,
            'Amos',
            'Baringbing',
            '************',
            'active'
        ]);
        
        $user_id = $pdo->lastInsertId();
        $found_user = [
            'id' => $user_id,
            'email' => '<EMAIL>',
            'first_name' => 'Amos',
            'last_name' => 'Baringbing'
        ];
        
        echo "<p class='text-success'>✅ Created Amos Baringbing account with ID: $user_id</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h6><i class='fas fa-check-circle me-2'></i>Found Amos Account</h6>";
        echo "<ul class='mb-0'>";
        echo "<li><strong>ID:</strong> " . $found_user['id'] . "</li>";
        echo "<li><strong>Email:</strong> " . $found_user['email'] . "</li>";
        echo "<li><strong>Name:</strong> " . $found_user['first_name'] . ' ' . $found_user['last_name'] . "</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Set session for Amos
    $_SESSION['user_id'] = $found_user['id'];
    $_SESSION['user_name'] = $found_user['first_name'] . ' ' . $found_user['last_name'];
    $_SESSION['user_email'] = $found_user['email'];
    
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle me-2'></i>Session Fixed!</h6>";
    echo "<ul class='mb-0'>";
    echo "<li><strong>Session user_id:</strong> " . $_SESSION['user_id'] . "</li>";
    echo "<li><strong>Session user_name:</strong> " . $_SESSION['user_name'] . "</li>";
    echo "<li><strong>Session user_email:</strong> " . $_SESSION['user_email'] . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Check/create wishlist table
    $stmt = $pdo->query("SHOW TABLES LIKE 'wishlist'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h6><i class='fas fa-wrench me-2'></i>Creating Wishlist Table</h6>";
        
        $pdo->exec("
            CREATE TABLE wishlist (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                product_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                UNIQUE KEY unique_wishlist (user_id, product_id),
                INDEX idx_user (user_id),
                INDEX idx_product (product_id)
            )
        ");
        echo "<p class='text-success'>✅ Wishlist table created</p>";
        echo "</div>";
    }
    
    // Add sample wishlist items
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM wishlist WHERE user_id = ?");
    $stmt->execute([$found_user['id']]);
    $wishlist_count = $stmt->fetchColumn();
    
    if ($wishlist_count == 0) {
        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-plus me-2'></i>Adding Sample Wishlist Items</h6>";
        
        // Get some products
        $stmt = $pdo->query("SELECT id, name FROM products WHERE status = 'active' LIMIT 3");
        $products = $stmt->fetchAll();
        
        if (!empty($products)) {
            foreach ($products as $product) {
                try {
                    $stmt = $pdo->prepare("INSERT IGNORE INTO wishlist (user_id, product_id) VALUES (?, ?)");
                    $stmt->execute([$found_user['id'], $product['id']]);
                    echo "<p class='text-success mb-1'>✅ Added '{$product['name']}' to wishlist</p>";
                } catch (Exception $e) {
                    echo "<p class='text-warning mb-1'>⚠️ Could not add '{$product['name']}': " . $e->getMessage() . "</p>";
                }
            }
        }
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>";
        echo "<h6><i class='fas fa-heart me-2'></i>Wishlist Status</h6>";
        echo "<p class='mb-0'>Amos already has <strong>$wishlist_count</strong> items in wishlist.</p>";
        echo "</div>";
    }
    
    echo "<div class='alert alert-success text-center'>";
    echo "<h4><i class='fas fa-check-circle me-2'></i>Wishlist Access Fixed!</h4>";
    echo "<p class='mb-3'>Amos Baringbing can now access the wishlist page without login issues.</p>";
    echo "<div class='d-flex justify-content-center gap-3'>";
    echo "<a href='wishlist.php' class='btn btn-primary btn-lg'>";
    echo "<i class='fas fa-heart me-2'></i>Go to Wishlist";
    echo "</a>";
    echo "<a href='test-wishlist.php' class='btn btn-success btn-lg'>";
    echo "<i class='fas fa-test-tube me-2'></i>Test Wishlist";
    echo "</a>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4><i class='fas fa-exclamation-triangle me-2'></i>Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='card mt-4'>";
echo "<div class='card-header'>";
echo "<h5 class='mb-0'>What Was Fixed</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h6 class='fw-bold mb-3'>The Problem</h6>";
echo "<ul class='small'>";
echo "<li>Header shows 'Amos Baringbing' (Firebase auth)</li>";
echo "<li>But PHP session doesn't have user_id</li>";
echo "<li>Wishlist page requires PHP session</li>";
echo "<li>Causes redirect to login page</li>";
echo "</ul>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h6 class='fw-bold mb-3'>The Solution</h6>";
echo "<ul class='small'>";
echo "<li>Found/created Amos account in database</li>";
echo "<li>Set proper PHP session variables</li>";
echo "<li>Created wishlist table if missing</li>";
echo "<li>Added sample wishlist items</li>";
echo "</ul>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
