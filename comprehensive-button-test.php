<?php
require_once 'config/config.php';

// Handle form submission
$test_results = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_order'])) {
    $test_results[] = "✅ Form submitted successfully";
    $test_results[] = "📝 POST data: " . print_r($_POST, true);
    $test_results[] = "🔄 Processing order simulation...";
    
    try {
        $pdo = getDBConnection();
        $test_results[] = "✅ Database connection established";
        
        // Test order insertion
        $order_number = 'TEST-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        $stmt = $pdo->prepare("
            INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount, 
                              tax_amount, discount_amount, payment_method, payment_status, 
                              shipping_address, notes, customer_info, created_at) 
            VALUES (?, ?, 'pending', ?, ?, ?, ?, ?, 'pending', ?, ?, ?, NOW())
        ");
        
        $result = $stmt->execute([
            $order_number,
            null,
            150000,
            15000,
            13500,
            0,
            'bank_transfer',
            'Test Address',
            'Test notes',
            json_encode(['test' => 'data'])
        ]);
        
        if ($result) {
            $order_id = $pdo->lastInsertId();
            $test_results[] = "✅ Order created successfully! Order ID: $order_id";
            $test_results[] = "✅ Order Number: $order_number";
            
            // Clean up
            $stmt = $pdo->prepare("DELETE FROM orders WHERE id = ?");
            $stmt->execute([$order_id]);
            $test_results[] = "🧹 Test order cleaned up";
        } else {
            $test_results[] = "❌ Order creation failed";
        }
        
    } catch (Exception $e) {
        $test_results[] = "❌ Error: " . $e->getMessage();
    }
}

// Set up test data
if (!isset($_SESSION['checkout_data'])) {
    $_SESSION['checkout_data'] = [
        'first_name' => 'Test',
        'last_name' => 'Customer',
        'email' => '<EMAIL>',
        'phone' => '+***********',
        'address_line_1' => 'Test Address 123',
        'city' => 'Jakarta',
        'state' => 'DKI Jakarta',
        'postal_code' => '12345',
        'country' => 'Indonesia',
        'payment_method' => 'bank_transfer',
        'notes' => 'Test order',
        'discount_amount' => 0,
        'coupon_id' => null
    ];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="text-center mb-4">
                    <i class="fas fa-bug me-2"></i>Comprehensive Button Test
                </h2>
                
                <!-- Test Results -->
                <?php if (!empty($test_results)): ?>
                <div class="test-section">
                    <h4><i class="fas fa-check-circle text-success me-2"></i>Test Results</h4>
                    <?php foreach ($test_results as $result): ?>
                        <p class="mb-1"><?php echo $result; ?></p>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <!-- System Status -->
                <div class="test-section">
                    <h4><i class="fas fa-info-circle text-info me-2"></i>System Status</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Session ID:</strong> <?php echo session_id(); ?></p>
                            <p><strong>Request Method:</strong> <?php echo $_SERVER['REQUEST_METHOD']; ?></p>
                            <p><strong>User ID:</strong> <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Guest'; ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Checkout Data:</strong> 
                                <span class="badge bg-<?php echo isset($_SESSION['checkout_data']) ? 'success' : 'danger'; ?>">
                                    <?php echo isset($_SESSION['checkout_data']) ? 'Available' : 'Missing'; ?>
                                </span>
                            </p>
                            <p><strong>POST Data:</strong> 
                                <span class="badge bg-<?php echo !empty($_POST) ? 'success' : 'secondary'; ?>">
                                    <?php echo !empty($_POST) ? 'Available' : 'None'; ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Button Test Form -->
                <div class="test-section">
                    <h4><i class="fas fa-mouse-pointer text-primary me-2"></i>Button Test</h4>
                    <form method="POST" id="testForm">
                        <div class="d-grid gap-2">
                            <button type="submit" name="confirm_order" value="1" class="btn btn-success btn-lg" id="testButton">
                                <i class="fas fa-check me-2"></i>Confirm & Place Order
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="testJavaScript()">
                                <i class="fas fa-code me-2"></i>Test JavaScript
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="clearConsole()">
                                <i class="fas fa-trash me-2"></i>Clear Console
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- JavaScript Console -->
                <div class="test-section">
                    <h4><i class="fas fa-terminal text-warning me-2"></i>JavaScript Console</h4>
                    <div id="console" class="console-output">
                        Console initialized...<br>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="test-section">
                    <h4><i class="fas fa-link text-secondary me-2"></i>Quick Links</h4>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="order-review.php" class="btn btn-primary btn-sm">Order Review</a>
                        <a href="order-review.php?debug=1" class="btn btn-info btn-sm">Order Review (Debug)</a>
                        <a href="setup-order-test.php" class="btn btn-secondary btn-sm">Setup Test</a>
                        <a href="check-orders-table.php" class="btn btn-warning btn-sm">Check Database</a>
                        <a href="index.php" class="btn btn-outline-secondary btn-sm">Home</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Console logging function
        function logToConsole(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += `[${timestamp}] ${message}<br>`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').innerHTML = 'Console cleared...<br>';
        }
        
        function testJavaScript() {
            logToConsole('JavaScript test started');
            logToConsole('✅ JavaScript is working');
            logToConsole('✅ Bootstrap loaded: ' + (typeof bootstrap !== 'undefined'));
            logToConsole('✅ Form found: ' + !!document.getElementById('testForm'));
            logToConsole('✅ Button found: ' + !!document.getElementById('testButton'));
        }
        
        // Page load event
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('Page loaded successfully');
            
            const form = document.getElementById('testForm');
            const button = document.getElementById('testButton');
            
            logToConsole('Form element: ' + (form ? 'Found' : 'Not found'));
            logToConsole('Button element: ' + (button ? 'Found' : 'Not found'));
            
            if (form && button) {
                // Button click handler
                button.addEventListener('click', function(e) {
                    logToConsole('Button clicked!');
                    logToConsole('Button disabled: ' + this.disabled);
                    logToConsole('Form method: ' + form.method);
                    logToConsole('Form action: ' + (form.action || 'current page'));
                });
                
                // Form submit handler
                form.addEventListener('submit', function(e) {
                    logToConsole('Form submit event triggered');
                    
                    // Show loading state
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    button.disabled = true;
                    
                    logToConsole('Loading state applied');
                    logToConsole('Form will submit in 100ms...');
                    
                    // Allow form to submit
                    return true;
                });
                
                logToConsole('Event handlers attached successfully');
            } else {
                logToConsole('❌ Failed to attach event handlers');
            }
        });
        
        // Error handling
        window.addEventListener('error', function(e) {
            logToConsole('❌ JavaScript Error: ' + e.message);
            logToConsole('❌ File: ' + e.filename + ':' + e.lineno);
        });
        
        // Initial test
        logToConsole('Script loaded successfully');
    </script>
</body>
</html>
