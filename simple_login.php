<?php
// Simple Login with Forced Redirect
require_once 'config/config.php';

$error_message = '';
$success_message = '';

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error_message = 'Please fill in all fields.';
    } else {
        try {
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("
                SELECT id, email, password, first_name, last_name, status 
                FROM users 
                WHERE email = ? AND status = 'active'
            ");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                
                // Force redirect using multiple methods
                $redirect_url = SITE_URL;
                
                // Method 1: PHP header redirect
                header("Location: $redirect_url");
                
                // Method 2: JavaScript redirect (backup)
                echo "<script>window.location.href = '$redirect_url';</script>";
                
                // Method 3: Meta refresh (backup)
                echo "<meta http-equiv='refresh' content='0;url=$redirect_url'>";
                
                // Method 4: Manual link (final backup)
                echo "<p>Login successful! <a href='$redirect_url'>Click here if not redirected automatically</a></p>";
                
                exit();
            } else {
                $error_message = 'Invalid email or password.';
            }
        } catch (Exception $e) {
            $error_message = 'Database error: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Login - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow">
                    <div class="card-body p-4">
                        <div class="text-center mb-4">
                            <h2 class="fw-bold text-primary">Simple Login</h2>
                            <p class="text-muted">Test login with redirect</p>
                        </div>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="user123" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-2"><strong>Test Accounts:</strong></p>
                            <small class="text-muted">
                                <EMAIL> / user123<br>
                                <EMAIL> / user123<br>
                                <EMAIL> / user123
                            </small>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="test_login_redirect.php" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-bug me-1"></i>Debug Test
                            </a>
                            <a href="index.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-home me-1"></i>Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
