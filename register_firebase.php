<?php
require_once 'config/config.php';

$page_title = 'Register';
$page_description = 'Create your TeWuNeed account to start shopping and enjoy exclusive benefits.';

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">Create Account</h2>
                        <p class="text-muted">Join <PERSON><PERSON>u<PERSON><PERSON> today</p>
                    </div>
                    
                    <!-- Message containers -->
                    <div id="error-container"></div>
                    <div id="success-container"></div>
                    
                    <form id="registerForm" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="firstName" name="firstName" required>
                                <div class="invalid-feedback">
                                    Please provide your first name.
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="lastName" name="lastName" required>
                                <div class="invalid-feedback">
                                    Please provide your last name.
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">
                                Please provide a valid email address.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" placeholder="+62 812 3456 7890">
                            <div class="form-text">Optional - for order updates</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Minimum 6 characters</div>
                            <div class="invalid-feedback">
                                Please provide a password (minimum 6 characters).
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                            <div class="invalid-feedback">
                                Please confirm your password.
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> and 
                                <a href="#" class="text-decoration-none">Privacy Policy</a>
                            </label>
                            <div class="invalid-feedback">
                                You must agree to the terms and conditions.
                            </div>
                        </div>
                        
                        <button type="submit" id="registerBtn" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Already have an account?</p>
                        <a href="login_firebase.php" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="module">
import { firebaseAuth, authHelpers } from './js/firebase-config.js';

document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const registerBtn = document.getElementById('registerBtn');
    const togglePassword = document.getElementById('togglePassword');
    
    // Toggle password visibility
    togglePassword.addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Handle registration form submission
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const firstName = document.getElementById('firstName').value.trim();
        const lastName = document.getElementById('lastName').value.trim();
        const email = document.getElementById('email').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const agreeTerms = document.getElementById('agreeTerms').checked;
        
        // Clear previous messages
        authHelpers.clearMessages();
        
        // Validate inputs
        if (!firstName || !lastName || !email || !password || !confirmPassword) {
            authHelpers.showError('Please fill in all required fields.');
            return;
        }
        
        if (!authHelpers.isValidEmail(email)) {
            authHelpers.showError('Please enter a valid email address.');
            return;
        }
        
        const passwordValidation = authHelpers.validatePassword(password);
        if (!passwordValidation.valid) {
            authHelpers.showError(passwordValidation.message);
            return;
        }
        
        if (password !== confirmPassword) {
            authHelpers.showError('Passwords do not match.');
            return;
        }
        
        if (!agreeTerms) {
            authHelpers.showError('You must agree to the terms and conditions.');
            return;
        }
        
        // Show loading state
        const originalText = registerBtn.innerHTML;
        authHelpers.showLoading(registerBtn, 'Creating Account...');
        
        try {
            // Attempt registration
            const result = await firebaseAuth.register(email, password, firstName, lastName, phone);
            
            if (result.success) {
                authHelpers.showSuccess('Account created successfully! Redirecting...');
                
                // Clear form
                registerForm.reset();
                
                // Redirect after short delay
                setTimeout(() => {
                    window.location.href = 'index.php';
                }, 2000);
            } else {
                authHelpers.showError(result.error);
                authHelpers.hideLoading(registerBtn, originalText);
            }
        } catch (error) {
            authHelpers.showError('An unexpected error occurred. Please try again.');
            authHelpers.hideLoading(registerBtn, originalText);
        }
    });
    
    // Real-time password confirmation validation
    document.getElementById('confirmPassword').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.setCustomValidity('Passwords do not match');
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
    });
    
    // Real-time password strength validation
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const validation = authHelpers.validatePassword(password);
        
        if (password && !validation.valid) {
            this.setCustomValidity(validation.message);
            this.classList.add('is-invalid');
        } else {
            this.setCustomValidity('');
            this.classList.remove('is-invalid');
        }
        
        // Also check confirm password if it has a value
        const confirmPassword = document.getElementById('confirmPassword');
        if (confirmPassword.value) {
            confirmPassword.dispatchEvent(new Event('input'));
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
