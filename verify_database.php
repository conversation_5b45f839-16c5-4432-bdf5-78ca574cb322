<?php
// Database Verification and Setup Script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Database Verification & Setup</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px;'>";

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'db_tewuneed2';

try {
    echo "<h2>Step 1: MySQL Connection Test</h2>";
    
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ MySQL server connection successful</p>";
    
    // Check if database exists
    echo "<h2>Step 2: Database Check</h2>";
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Database '$database' exists</p>";
        $pdo->exec("USE $database");
    } else {
        echo "<p style='color: orange;'>⚠ Database '$database' does not exist. Creating...</p>";
        $pdo->exec("CREATE DATABASE `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE $database");
        echo "<p style='color: green;'>✓ Database '$database' created</p>";
    }
    
    // Check tables
    echo "<h2>Step 3: Table Structure Check</h2>";
    $required_tables = ['users', 'categories', 'products'];
    $missing_tables = [];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Table '$table' exists</p>";
            
            // Check users table structure specifically
            if ($table === 'users') {
                $stmt = $pdo->query("DESCRIBE users");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $required_columns = ['id', 'email', 'password', 'first_name', 'last_name', 'status'];
                $missing_columns = array_diff($required_columns, $columns);
                
                if (empty($missing_columns)) {
                    echo "<p style='color: green;'>✓ Users table has correct structure</p>";
                } else {
                    echo "<p style='color: red;'>✗ Users table missing columns: " . implode(', ', $missing_columns) . "</p>";
                    $missing_tables[] = $table;
                }
            }
        } else {
            echo "<p style='color: red;'>✗ Table '$table' missing</p>";
            $missing_tables[] = $table;
        }
    }
    
    // Create missing tables
    if (!empty($missing_tables)) {
        echo "<h2>Step 4: Creating Missing Tables</h2>";
        
        // Drop and recreate all tables to ensure consistency
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        $pdo->exec("DROP TABLE IF EXISTS products");
        $pdo->exec("DROP TABLE IF EXISTS categories");
        $pdo->exec("DROP TABLE IF EXISTS users");
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        
        // Create users table
        $pdo->exec("
            CREATE TABLE `users` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
              `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
              `first_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
              `last_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
              `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `date_of_birth` date DEFAULT NULL,
              `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `email_verified` tinyint(1) DEFAULT 0,
              `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `email` (`email`),
              KEY `idx_email` (`email`),
              KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✓ Users table created</p>";
        
        // Create categories table
        $pdo->exec("
            CREATE TABLE `categories` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
              `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
              `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `slug` (`slug`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✓ Categories table created</p>";
        
        // Create products table
        $pdo->exec("
            CREATE TABLE `products` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
              `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
              `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `short_description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `price` decimal(10,2) NOT NULL,
              `sale_price` decimal(10,2) DEFAULT NULL,
              `sku` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
              `stock_quantity` int(11) DEFAULT 0,
              `category_id` int(11) DEFAULT NULL,
              `images` json DEFAULT NULL,
              `featured` tinyint(1) DEFAULT 0,
              `status` enum('active','inactive','draft') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `slug` (`slug`),
              KEY `idx_category` (`category_id`),
              KEY `idx_status` (`status`),
              KEY `idx_featured` (`featured`),
              CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<p style='color: green;'>✓ Products table created</p>";
    }
    
    // Check and create test users
    echo "<h2>Step 5: Test Users Setup</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        echo "<p style='color: orange;'>⚠ No users found. Creating test users...</p>";
        
        // Create test users with known password hash
        $password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // user123
        
        $users = [
            ['<EMAIL>', 'Test', 'User'],
            ['<EMAIL>', 'John', 'Doe'],
            ['<EMAIL>', 'Jane', 'Smith']
        ];
        
        foreach ($users as $user) {
            $stmt = $pdo->prepare("
                INSERT INTO users (email, password, first_name, last_name, phone, email_verified, status) 
                VALUES (?, ?, ?, ?, '081234567890', 1, 'active')
            ");
            $stmt->execute([$user[0], $password_hash, $user[1], $user[2]]);
            echo "<p style='color: green;'>✓ Created user: " . $user[0] . " (password: user123)</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Found " . $result['count'] . " existing users</p>";
        
        // Update passwords to ensure they work
        $password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // user123
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
        $stmt->execute([$password_hash]);
        echo "<p style='color: blue;'>→ Updated passwords for test users</p>";
    }
    
    // Add sample data if needed
    echo "<h2>Step 6: Sample Data Setup</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        $pdo->exec("
            INSERT INTO categories (name, slug, description, status) VALUES
            ('Electronics', 'electronics', 'Electronic devices and gadgets', 'active'),
            ('Cosmetics', 'cosmetics', 'Beauty and cosmetic products', 'active'),
            ('Sports', 'sports', 'Sports and fitness equipment', 'active')
        ");
        echo "<p style='color: green;'>✓ Sample categories created</p>";
    }
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $result = $stmt->fetch();
    
    if ($result['count'] == 0) {
        $pdo->exec("
            INSERT INTO products (name, slug, description, price, category_id, featured, sku) VALUES
            ('iPhone 14 Pro', 'iphone-14-pro', 'Latest iPhone with advanced features', 14000000, 1, 1, 'IP14PRO001'),
            ('Samsung Galaxy S23', 'samsung-galaxy-s23', 'Flagship Android smartphone', 12000000, 1, 1, 'SGS23001'),
            ('Vitamin C Serum', 'vitamin-c-serum', 'Anti-aging skincare serum', 250000, 2, 1, 'VCS001')
        ");
        echo "<p style='color: green;'>✓ Sample products created</p>";
    }
    
    // Final verification
    echo "<h2>Step 7: Final Verification</h2>";
    $test_email = '<EMAIL>';
    $test_password = 'user123';
    
    $stmt = $pdo->prepare("
        SELECT id, email, password, first_name, last_name, status 
        FROM users 
        WHERE email = ? AND status = 'active'
    ");
    $stmt->execute([$test_email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($test_password, $user['password'])) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 DATABASE SETUP COMPLETE!</p>";
        echo "<p style='color: green;'>✓ Login system is ready to use</p>";
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>✅ Setup Successful!</h3>";
        echo "<p><strong>Test Accounts (Password: user123):</strong></p>";
        echo "<ul>";
        echo "<li><EMAIL></li>";
        echo "<li><EMAIL></li>";
        echo "<li><EMAIL></li>";
        echo "</ul>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ol>";
        echo "<li><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Login Page</a></li>";
        echo "<li><a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Visit Home Page</a></li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ Login verification failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ CRITICAL ERROR: " . $e->getMessage() . "</p>";
    echo "<p>Please ensure:</p>";
    echo "<ul>";
    echo "<li>XAMPP is running</li>";
    echo "<li>MySQL service is started</li>";
    echo "<li>Database credentials are correct</li>";
    echo "</ul>";
}

echo "</div>";
?>
