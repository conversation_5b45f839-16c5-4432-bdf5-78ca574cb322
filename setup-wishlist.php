<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Setup Wishlist System</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>💝 Setup Wishlist System</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='step'>";
    echo "<h2>Step 1: Create Wishlist Table</h2>";
    
    // Check if wishlist table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'wishlist'");
    if ($stmt->rowCount() == 0) {
        echo "<p class='warning'>⚠️ Wishlist table not found, creating...</p>";
        
        $pdo->exec("
            CREATE TABLE wishlist (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                product_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                UNIQUE KEY unique_wishlist (user_id, product_id),
                INDEX idx_user (user_id),
                INDEX idx_product (product_id)
            )
        ");
        echo "<p class='success'>✅ Wishlist table created successfully</p>";
    } else {
        echo "<p class='success'>✅ Wishlist table already exists</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Add Sample Wishlist Items</h2>";
    
    // Check if test user exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $test_user = $stmt->fetch();
    
    if (!$test_user) {
        echo "<p class='warning'>⚠️ Test user not found, creating...</p>";
        $password = password_hash('password123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, first_name, last_name, phone, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute(['<EMAIL>', $password, 'Test', 'User', '081234567890', 'active']);
        $test_user_id = $pdo->lastInsertId();
        echo "<p class='success'>✅ Test user created with ID: $test_user_id</p>";
    } else {
        $test_user_id = $test_user['id'];
        echo "<p class='success'>✅ Test user found with ID: $test_user_id</p>";
    }
    
    // Get some products for wishlist
    $stmt = $pdo->query("SELECT id, name FROM products WHERE status = 'active' LIMIT 3");
    $products = $stmt->fetchAll();
    
    if (!empty($products)) {
        echo "<p class='info'>Adding sample wishlist items...</p>";
        
        foreach ($products as $product) {
            try {
                $stmt = $pdo->prepare("
                    INSERT IGNORE INTO wishlist (user_id, product_id) 
                    VALUES (?, ?)
                ");
                $stmt->execute([$test_user_id, $product['id']]);
                echo "<p class='success'>✅ Added '{$product['name']}' to wishlist</p>";
            } catch (Exception $e) {
                echo "<p class='warning'>⚠️ '{$product['name']}' already in wishlist or error: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p class='warning'>⚠️ No products found to add to wishlist</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Test Wishlist Query</h2>";
    
    $sql = "
        SELECT w.*, p.name, p.price, p.sale_price, p.image, p.slug, p.stock_quantity,
               c.name as category_name
        FROM wishlist w
        JOIN products p ON w.product_id = p.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE w.user_id = ?
        ORDER BY w.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$test_user_id]);
    $wishlist_items = $stmt->fetchAll();
    
    if (!empty($wishlist_items)) {
        echo "<p class='success'>✅ Wishlist query successful! Found " . count($wishlist_items) . " items</p>";
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped table-sm'>";
        echo "<thead><tr><th>Product</th><th>Price</th><th>Category</th><th>Added</th></tr></thead>";
        echo "<tbody>";
        
        foreach ($wishlist_items as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>" . formatPrice($item['price']) . "</td>";
            echo "<td>" . htmlspecialchars($item['category_name'] ?? 'No category') . "</td>";
            echo "<td>" . formatDate($item['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
    } else {
        echo "<p class='warning'>⚠️ No wishlist items found</p>";
    }
    echo "</div>";
    
    echo "<div class='alert alert-success text-center'>";
    echo "<h3>🎉 Wishlist System Setup Complete!</h3>";
    echo "<p>The wishlist system is now ready to use.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-5'>";
echo "<h4 class='mb-4'>Test Your Wishlist System!</h4>";
echo "<div class='d-flex justify-content-center gap-3 flex-wrap'>";
echo "<a href='wishlist.php' class='btn btn-success btn-lg'><i class='fas fa-heart me-2'></i>View Wishlist</a>";
echo "<a href='products.php' class='btn btn-primary btn-lg'><i class='fas fa-shopping-bag me-2'></i>Browse Products</a>";
echo "<a href='login.php' class='btn btn-info btn-lg'><i class='fas fa-sign-in-alt me-2'></i>Login</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
