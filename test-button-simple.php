<?php
require_once 'config/config.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_order'])) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; color: #155724;'>";
    echo "<h3>✅ Button Works!</h3>";
    echo "<p>Form was submitted successfully.</p>";
    echo "<p><strong>POST Data:</strong></p>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Button</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Test Confirm & Place Order Button</h5>
                    </div>
                    <div class="card-body">
                        <p>This is a simple test to check if the button works.</p>
                        
                        <form method="POST" id="testForm">
                            <div class="d-grid">
                                <button type="submit" name="confirm_order" value="1" class="btn btn-success btn-lg" id="testButton">
                                    <i class="fas fa-check me-2"></i>Confirm & Place Order
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-3">
                            <p><strong>Debug Info:</strong></p>
                            <ul>
                                <li>Request Method: <?php echo $_SERVER['REQUEST_METHOD']; ?></li>
                                <li>Session ID: <?php echo session_id(); ?></li>
                                <li>POST Data: <?php echo empty($_POST) ? 'None' : 'Available'; ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded');
            
            const form = document.getElementById('testForm');
            const button = document.getElementById('testButton');
            
            console.log('Form found:', !!form);
            console.log('Button found:', !!button);
            
            if (form && button) {
                button.addEventListener('click', function(e) {
                    console.log('Button clicked!');
                });
                
                form.addEventListener('submit', function(e) {
                    console.log('Form submitted!');
                    
                    // Show loading state
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    button.disabled = true;
                });
            }
        });
    </script>
</body>
</html>
