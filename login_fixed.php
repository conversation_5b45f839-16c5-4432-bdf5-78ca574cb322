<?php
// FIXED LOGIN PAGE - Handles redirect properly
require_once 'config/config.php';

$page_title = 'Login';
$page_description = 'Login to your TeWuNeed account';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: " . SITE_URL);
    exit();
}

$error_message = '';
$login_success = false;
$redirect_url = SITE_URL;

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($email) || empty($password)) {
        $error_message = 'Please fill in all required fields.';
    } else {
        try {
            $pdo = getDBConnection();
            
            $stmt = $pdo->prepare("
                SELECT id, email, password, first_name, last_name, status 
                FROM users 
                WHERE email = ? AND status = 'active'
            ");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password'])) {
                // Login successful - set session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                
                // Set remember me cookie if requested
                if ($remember_me) {
                    $token = bin2hex(random_bytes(32));
                    setcookie('remember_token', $token, time() + 30 * 24 * 60 * 60, '/');
                }
                
                // Set success flag for JavaScript redirect
                $login_success = true;
                $redirect_url = $_SESSION['intended_url'] ?? SITE_URL;
                unset($_SESSION['intended_url']);
                
            } else {
                $error_message = 'Invalid email or password.';
            }
        } catch (Exception $e) {
            $error_message = 'An error occurred. Please try again.';
        }
    }
}

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-primary">Welcome Back</h2>
                        <p class="text-muted">Sign in to your account</p>
                    </div>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($login_success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            Login successful! Redirecting to home page...
                        </div>
                        <script>
                            // Immediate redirect
                            window.location.href = '<?php echo $redirect_url; ?>';
                        </script>
                        <noscript>
                            <meta http-equiv="refresh" content="0;url=<?php echo $redirect_url; ?>">
                        </noscript>
                    <?php else: ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : '<EMAIL>'; ?>" required>
                            <div class="invalid-feedback">
                                Please provide a valid email address.
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="user123" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Please provide your password.
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                Remember me
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">Don't have an account?</p>
                        <a href="register.php" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </a>
                    </div>
                    
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Demo Accounts Info -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title">Demo Accounts</h6>
                    <small class="text-muted">
                        <strong>Test User:</strong> <EMAIL> / user123<br>
                        <strong>John Doe:</strong> <EMAIL> / user123<br>
                        <strong>Jane Smith:</strong> <EMAIL> / user123
                    </small>
                </div>
            </div>
            
            <!-- Quick Test Links -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <h6 class="card-title">Quick Tests</h6>
                    <a href="test_login_redirect.php" class="btn btn-outline-info btn-sm me-2">
                        <i class="fas fa-bug me-1"></i>Debug Test
                    </a>
                    <a href="simple_login.php" class="btn btn-outline-warning btn-sm me-2">
                        <i class="fas fa-bolt me-1"></i>Simple Login
                    </a>
                    <a href="index.php" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-home me-1"></i>Home Page
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }
    
    // Form validation
    const form = document.querySelector('.needs-validation');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
