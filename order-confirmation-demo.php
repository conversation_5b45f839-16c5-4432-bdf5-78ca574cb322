<?php
// Demo page to showcase the enhanced order confirmation design
require_once 'config/config.php';

// Sample order data for demonstration
$order = [
    'id' => 1,
    'order_number' => 'TWN-2024-0001',
    'status' => 'pending',
    'payment_status' => 'pending',
    'payment_method' => 'Bank Transfer BCA',
    'total_amount' => 385000,
    'shipping_amount' => 15000,
    'tax_amount' => 35000,
    'discount_amount' => 0,
    'shipping_address' => "<PERSON>\nJl. Sudirman No. 123\nJakarta Pusat, DKI Jakarta 10110\nIndonesia\nPhone: +62 812 3456 7890",
    'created_at' => date('Y-m-d H:i:s'),
    'notes' => 'Please deliver during office hours'
];

// Sample order items
$order_items = [
    [
        'product_name' => 'Sony Headphones WH-1000XM4',
        'image' => 'sony-headphones.jpg',
        'quantity' => 1,
        'price' => 200000,
        'total' => 200000
    ],
    [
        'product_name' => 'MacBook Air M2',
        'image' => 'macbook-air.jpg',
        'quantity' => 1,
        'price' => 170000,
        'total' => 170000
    ]
];

// Sample customer info
$customer_info = [
    'first_name' => 'John',
    'last_name' => 'Doe',
    'email' => '<EMAIL>',
    'phone' => '+62 812 3456 7890'
];

$page_title = 'Order Confirmation - Demo';
include 'includes/header.php';
?>

<!-- Custom Styles -->
<style>
.order-success-animation {
    animation: bounceIn 1s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

.order-timeline {
    position: relative;
    padding-left: 30px;
}

.order-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #28a745, #20c997);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
    border: 3px solid #fff;
    box-shadow: 0 0 0 3px #28a745;
}

.timeline-item.pending::before {
    background: #ffc107;
    box-shadow: 0 0 0 3px #ffc107;
}

.payment-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
}

.order-summary-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 15px;
}

.success-badge {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
    margin-bottom: 20px;
}

.order-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.order-card:hover {
    transform: translateY(-5px);
}

.print-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.print-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

@media print {
    .no-print { display: none !important; }
    .order-card { box-shadow: none !important; border: 1px solid #ddd !important; }
}
</style>

<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Demo Notice -->
            <div class="alert alert-info border-0 mb-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Demo Page:</strong> This is a demonstration of the enhanced order confirmation page design.
            </div>

            <!-- Success Message with Animation -->
            <div class="text-center mb-5 order-success-animation">
                <div class="mb-4">
                    <div class="success-badge">
                        <i class="fas fa-check-circle me-2"></i>Order Confirmed
                    </div>
                </div>
                <h1 class="text-success mb-3">🎉 Thank You for Your Order!</h1>
                <p class="lead">Your order has been successfully placed and is being processed.</p>
                <div class="alert alert-success border-0" style="background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <strong>Order Number:</strong><br>
                            <span class="h5 text-success order-number-clickable"><?php echo htmlspecialchars($order['order_number']); ?></span>
                        </div>
                        <div class="col-md-6">
                            <strong>Order Date:</strong><br>
                            <span class="h6"><?php echo formatDate($order['created_at']); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info border-0 mt-3">
                    <i class="fas fa-user me-2"></i>
                    <strong>Order for:</strong> <?php echo htmlspecialchars($customer_info['first_name'] . ' ' . $customer_info['last_name']); ?>
                    <br><small>Confirmation sent to: <?php echo htmlspecialchars($customer_info['email']); ?></small>
                </div>
            </div>

            <!-- Payment Instructions -->
            <div class="order-card mb-4">
                <div class="payment-card">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-credit-card me-3" style="font-size: 2rem;"></i>
                        <div>
                            <h5 class="mb-0">Payment Required</h5>
                            <small class="opacity-75">Complete your payment to process your order</small>
                        </div>
                    </div>

                    <div class="payment-instructions">
                        <h6 class="mb-3"><i class="fas fa-university me-2"></i>Bank Transfer Instructions</h6>
                        <div class="bg-white bg-opacity-20 p-4 rounded-3 mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Bank BCA</strong><br>
                                    <small class="opacity-75">Account Number</small><br>
                                    <span class="h5">**********</span><br>
                                    <small class="opacity-75">Account Name</small><br>
                                    <strong>PT TeWuNeed Indonesia</strong>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <small class="opacity-75">Amount to Transfer</small><br>
                                    <span class="h4"><?php echo formatPrice($order['total_amount']); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-light border-0 bg-white bg-opacity-20">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Important:</strong> Include order number <strong><?php echo $order['order_number']; ?></strong> in transfer description
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Status Timeline -->
            <div class="order-card mb-4">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2 text-primary"></i>Order Status & Timeline
                    </h5>
                </div>
                <div class="card-body">
                    <div class="order-timeline">
                        <div class="timeline-item">
                            <h6 class="mb-1">Order Placed</h6>
                            <small class="text-muted"><?php echo formatDate($order['created_at']); ?></small>
                            <p class="mb-0 text-success"><i class="fas fa-check me-1"></i>Completed</p>
                        </div>
                        
                        <div class="timeline-item pending">
                            <h6 class="mb-1">Payment Confirmation</h6>
                            <small class="text-muted">Waiting for payment</small>
                            <p class="mb-0 text-warning">
                                <i class="fas fa-clock me-1"></i>Pending
                            </p>
                        </div>
                        
                        <div class="timeline-item pending">
                            <h6 class="mb-1">Order Processing</h6>
                            <small class="text-muted">Preparing your items</small>
                            <p class="mb-0 text-muted"><i class="fas fa-clock me-1"></i>Pending</p>
                        </div>
                        
                        <div class="timeline-item pending">
                            <h6 class="mb-1">Shipped</h6>
                            <small class="text-muted">On the way to you</small>
                            <p class="mb-0 text-muted"><i class="fas fa-clock me-1"></i>Pending</p>
                        </div>
                        
                        <div class="timeline-item pending">
                            <h6 class="mb-1">Delivered</h6>
                            <small class="text-muted">Estimated: 2-5 business days</small>
                            <p class="mb-0 text-muted"><i class="fas fa-clock me-1"></i>Pending</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="order-card mb-4">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-bag me-2 text-primary"></i>Order Items
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($order_items as $item): ?>
                        <div class="d-flex align-items-center mb-3 pb-3 border-bottom">
                            <img src="uploads/<?php echo $item['image'] ?: 'default-product.jpg'; ?>" 
                                 alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                 class="rounded me-3" style="width: 80px; height: 80px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo htmlspecialchars($item['product_name']); ?></h6>
                                <p class="text-muted mb-1">Quantity: <?php echo $item['quantity']; ?></p>
                                <p class="text-muted mb-0">Price: <?php echo formatPrice($item['price']); ?></p>
                            </div>
                            <div class="text-end">
                                <strong><?php echo formatPrice($item['total']); ?></strong>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <!-- Order Summary -->
                    <div class="row mt-3">
                        <div class="col-md-6 offset-md-6">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span><?php echo formatPrice($order['total_amount'] - $order['shipping_amount'] - $order['tax_amount']); ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span><?php echo formatPrice($order['shipping_amount']); ?></span>
                            </div>
                            
                            <div class="d-flex justify-content-between mb-2">
                                <span>Tax:</span>
                                <span><?php echo formatPrice($order['tax_amount']); ?></span>
                            </div>
                            
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total:</strong>
                                <strong class="text-primary"><?php echo formatPrice($order['total_amount']); ?></strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center no-print">
                <div class="row g-3 justify-content-center">
                    <div class="col-md-4">
                        <a href="my-orders.php" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-list me-2"></i>View My Orders
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="products.php" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-shopping-cart me-2"></i>Continue Shopping
                        </a>
                    </div>
                    <div class="col-md-4">
                        <button onclick="window.print()" class="btn print-button btn-lg w-100">
                            <i class="fas fa-print me-2"></i>Print Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Copy order number to clipboard
function copyOrderNumber() {
    const orderNumber = '<?php echo $order['order_number']; ?>';
    navigator.clipboard.writeText(orderNumber).then(function() {
        // Show success message
        const alert = document.createElement('div');
        alert.className = 'alert alert-success position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; animation: fadeInOut 3s ease-in-out;';
        alert.innerHTML = '<i class="fas fa-check me-2"></i>Order number copied to clipboard!';
        document.body.appendChild(alert);
        
        setTimeout(() => {
            document.body.removeChild(alert);
        }, 3000);
    });
}

// Add click handler to order number
document.addEventListener('DOMContentLoaded', function() {
    const orderNumberElements = document.querySelectorAll('.order-number-clickable');
    orderNumberElements.forEach(element => {
        element.style.cursor = 'pointer';
        element.title = 'Click to copy';
        element.addEventListener('click', copyOrderNumber);
    });
});
</script>

<style>
@keyframes fadeInOut {
    0% { opacity: 0; transform: translateX(100%); }
    20% { opacity: 1; transform: translateX(0); }
    80% { opacity: 1; transform: translateX(0); }
    100% { opacity: 0; transform: translateX(100%); }
}
</style>

<?php include 'includes/footer.php'; ?>
