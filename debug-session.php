<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Session Debug - <PERSON>bing Issue</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        .debug-section { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔍 Session Debug - <PERSON>bing Issue</h1>";

echo "<div class='debug-section'>";
echo "<h2>1. Current Session Information</h2>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>Session Status</h5>";
echo "<ul class='list-unstyled'>";
echo "<li><strong>Session ID:</strong> <code>" . session_id() . "</code></li>";
echo "<li><strong>Session Status:</strong> " . session_status() . "</li>";
echo "<li><strong>Session Started:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . "</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h5>Session Variables</h5>";
if (!empty($_SESSION)) {
    echo "<ul class='list-unstyled'>";
    foreach ($_SESSION as $key => $value) {
        echo "<li><strong>$key:</strong> " . htmlspecialchars(is_array($value) ? json_encode($value) : $value) . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p class='warning'>⚠️ No session variables found</p>";
}
echo "</div>";
echo "</div>";
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>2. User Authentication Check</h2>";

if (isset($_SESSION['user_id'])) {
    echo "<div class='alert alert-success'>";
    echo "<h5><i class='fas fa-check-circle me-2'></i>User is Logged In</h5>";
    echo "<ul class='mb-0'>";
    echo "<li><strong>User ID:</strong> " . $_SESSION['user_id'] . "</li>";
    echo "<li><strong>User Name:</strong> " . ($_SESSION['user_name'] ?? 'Not set') . "</li>";
    echo "<li><strong>User Email:</strong> " . ($_SESSION['user_email'] ?? 'Not set') . "</li>";
    echo "</ul>";
    echo "</div>";
    
    // Check if user exists in database
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if ($user) {
            echo "<div class='alert alert-success'>";
            echo "<h6><i class='fas fa-database me-2'></i>User Found in Database</h6>";
            echo "<ul class='mb-0'>";
            echo "<li><strong>Database ID:</strong> " . $user['id'] . "</li>";
            echo "<li><strong>Email:</strong> " . $user['email'] . "</li>";
            echo "<li><strong>Name:</strong> " . $user['first_name'] . ' ' . $user['last_name'] . "</li>";
            echo "<li><strong>Status:</strong> " . $user['status'] . "</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div class='alert alert-danger'>";
            echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>User NOT Found in Database</h6>";
            echo "<p>Session user_id (" . $_SESSION['user_id'] . ") does not exist in users table</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Database Error</h6>";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h5><i class='fas fa-exclamation-triangle me-2'></i>User is NOT Logged In</h5>";
    echo "<p>No user_id found in session</p>";
    echo "</div>";
}
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>3. My Orders Page Access Test</h2>";

if (isset($_SESSION['user_id'])) {
    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-info-circle me-2'></i>Testing My Orders Access</h6>";
    echo "<p>Since you are logged in, My Orders page should work. Let's test:</p>";
    echo "<div class='d-flex gap-3'>";
    echo "<a href='my-orders.php' class='btn btn-primary'><i class='fas fa-shopping-bag me-2'></i>Test My Orders</a>";
    echo "<a href='my-orders-working.php' class='btn btn-success'><i class='fas fa-rocket me-2'></i>Working Version</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div class='alert alert-warning'>";
    echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Cannot Test My Orders</h6>";
    echo "<p>You need to login first. Here are your options:</p>";
    echo "<div class='d-flex gap-3'>";
    echo "<a href='login.php' class='btn btn-primary'><i class='fas fa-sign-in-alt me-2'></i>Login Page</a>";
    echo "<a href='complete-fix-my-orders.php' class='btn btn-warning'><i class='fas fa-wrench me-2'></i>Auto Setup & Login</a>";
    echo "</div>";
    echo "</div>";
}
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>4. Database Connection Test</h2>";

try {
    $pdo = getDBConnection();
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle me-2'></i>Database Connection Successful</h6>";
    
    // Check tables
    $tables = ['users', 'orders', 'order_items', 'products'];
    echo "<h6>Table Status:</h6>";
    echo "<ul class='mb-0'>";
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<li><strong>$table:</strong> <span class='success'>✅ $count records</span></li>";
        } catch (Exception $e) {
            echo "<li><strong>$table:</strong> <span class='error'>❌ Error: " . $e->getMessage() . "</span></li>";
        }
    }
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Database Connection Failed</h6>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>5. Possible Solutions</h2>";

if (!isset($_SESSION['user_id'])) {
    echo "<div class='alert alert-info'>";
    echo "<h6><i class='fas fa-lightbulb me-2'></i>You Need to Login</h6>";
    echo "<p>The issue is that you're not logged in. Even though you see 'Amos Baringbing' in the header, the session might not be properly set.</p>";
    echo "<h6>Solutions:</h6>";
    echo "<ol>";
    echo "<li><strong>Login Again:</strong> <a href='login.php' class='btn btn-sm btn-primary'>Go to Login</a></li>";
    echo "<li><strong>Auto Setup:</strong> <a href='complete-fix-my-orders.php' class='btn btn-sm btn-warning'>Complete Fix</a></li>";
    echo "<li><strong>Clear Session:</strong> <a href='logout.php' class='btn btn-sm btn-secondary'>Logout & Try Again</a></li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div class='alert alert-success'>";
    echo "<h6><i class='fas fa-check-circle me-2'></i>Session Looks Good</h6>";
    echo "<p>Your session appears to be working. If My Orders still redirects to login, there might be another issue.</p>";
    echo "<h6>Try These:</h6>";
    echo "<ol>";
    echo "<li><strong>Test My Orders:</strong> <a href='my-orders.php' class='btn btn-sm btn-primary'>My Orders</a></li>";
    echo "<li><strong>Working Version:</strong> <a href='my-orders-working.php' class='btn btn-sm btn-success'>Guaranteed Working</a></li>";
    echo "<li><strong>Debug Version:</strong> <a href='my-orders-debug.php' class='btn btn-sm btn-info'>Debug My Orders</a></li>";
    echo "</ol>";
    echo "</div>";
}
echo "</div>";

echo "<div class='debug-section'>";
echo "<h2>6. Header Display Issue</h2>";
echo "<div class='alert alert-warning'>";
echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Possible Header Issue</h6>";
echo "<p>If you see 'Amos Baringbing' in the header but session shows no user_id, this could be:</p>";
echo "<ul class='mb-0'>";
echo "<li><strong>Firebase Auth:</strong> Header might be using Firebase authentication</li>";
echo "<li><strong>JavaScript Display:</strong> Header might be populated by JavaScript</li>";
echo "<li><strong>Cached Display:</strong> Browser might be showing cached header</li>";
echo "<li><strong>Different Session:</strong> Header and My Orders might use different authentication</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='text-center mt-5'>";
echo "<h4 class='mb-4'>Quick Actions</h4>";
echo "<div class='d-flex justify-content-center gap-3 flex-wrap'>";
echo "<a href='login.php' class='btn btn-primary btn-lg'><i class='fas fa-sign-in-alt me-2'></i>Login Page</a>";
echo "<a href='my-orders.php' class='btn btn-success btn-lg'><i class='fas fa-shopping-bag me-2'></i>My Orders</a>";
echo "<a href='complete-fix-my-orders.php' class='btn btn-warning btn-lg'><i class='fas fa-wrench me-2'></i>Complete Fix</a>";
echo "<a href='logout.php' class='btn btn-secondary btn-lg'><i class='fas fa-sign-out-alt me-2'></i>Logout & Reset</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
