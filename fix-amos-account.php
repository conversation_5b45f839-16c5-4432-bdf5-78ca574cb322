<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Amos Baringbing Account</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        .step { background: #f8f9fa; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔧 Fix Amos Baringbing Account Access</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='step'>";
    echo "<h2>Step 1: Check Current Session</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<p class='success'>✅ User is logged in with ID: " . $_SESSION['user_id'] . "</p>";
        echo "<p>User Name: " . ($_SESSION['user_name'] ?? 'Not set') . "</p>";
        echo "<p>User Email: " . ($_SESSION['user_email'] ?? 'Not set') . "</p>";
    } else {
        echo "<p class='warning'>⚠️ No user logged in currently</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Find Amos Baringbing Account</h2>";
    
    // Search for Amos Baringbing account
    $search_terms = [
        "<EMAIL>",
        "amos%",
        "%baringbing%",
        "%amos%"
    ];
    
    $found_user = null;
    foreach ($search_terms as $term) {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email LIKE ? OR first_name LIKE ? OR last_name LIKE ?");
        $stmt->execute([$term, $term, $term]);
        $users = $stmt->fetchAll();
        
        if (!empty($users)) {
            echo "<p class='success'>✅ Found user(s) with search term: '$term'</p>";
            foreach ($users as $user) {
                echo "<div class='alert alert-info'>";
                echo "<strong>User Found:</strong><br>";
                echo "ID: " . $user['id'] . "<br>";
                echo "Email: " . $user['email'] . "<br>";
                echo "Name: " . $user['first_name'] . ' ' . $user['last_name'] . "<br>";
                echo "Status: " . $user['status'] . "<br>";
                echo "</div>";
                
                if (stripos($user['email'], 'amos') !== false || stripos($user['first_name'], 'amos') !== false) {
                    $found_user = $user;
                }
            }
        }
    }
    
    if (!$found_user) {
        echo "<p class='warning'>⚠️ Amos Baringbing account not found, creating one...</p>";
        
        // Create Amos Baringbing account
        $password = password_hash('amos123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (email, password, first_name, last_name, phone, status) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            '<EMAIL>',
            $password,
            'Amos',
            'Baringbing',
            '************',
            'active'
        ]);
        
        $user_id = $pdo->lastInsertId();
        echo "<p class='success'>✅ Created Amos Baringbing account with ID: $user_id</p>";
        echo "<p><strong>Login Credentials:</strong></p>";
        echo "<ul>";
        echo "<li>Email: <EMAIL></li>";
        echo "<li>Password: amos123</li>";
        echo "</ul>";
        
        $found_user = [
            'id' => $user_id,
            'email' => '<EMAIL>',
            'first_name' => 'Amos',
            'last_name' => 'Baringbing'
        ];
    } else {
        echo "<p class='success'>✅ Found Amos Baringbing account</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Auto-Login Amos Account</h2>";
    
    if ($found_user) {
        // Set session for Amos
        $_SESSION['user_id'] = $found_user['id'];
        $_SESSION['user_name'] = $found_user['first_name'] . ' ' . $found_user['last_name'];
        $_SESSION['user_email'] = $found_user['email'];
        
        echo "<p class='success'>✅ Auto-logged in Amos Baringbing</p>";
        echo "<ul>";
        echo "<li>Session user_id: " . $_SESSION['user_id'] . "</li>";
        echo "<li>Session user_name: " . $_SESSION['user_name'] . "</li>";
        echo "<li>Session user_email: " . $_SESSION['user_email'] . "</li>";
        echo "</ul>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Create Sample Orders for Amos</h2>";
    
    if ($found_user) {
        $user_id = $found_user['id'];
        
        // Check if orders exist
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $order_count = $stmt->fetchColumn();
        
        if ($order_count == 0) {
            echo "<p class='warning'>⚠️ No orders found for Amos, creating sample orders...</p>";
            
            // Ensure products exist
            $stmt = $pdo->query("SELECT COUNT(*) FROM products");
            $product_count = $stmt->fetchColumn();
            
            if ($product_count == 0) {
                echo "<p class='warning'>⚠️ No products found, creating sample products...</p>";
                $products = [
                    ['Samsung Galaxy S24', 'samsung-galaxy-s24', 'Latest Samsung flagship smartphone', 'Premium smartphone', 15000000, 'SGS24-001'],
                    ['iPhone 15 Pro', 'iphone-15-pro', 'Apple iPhone 15 Pro', 'Latest iPhone', 18000000, 'IP15P-001'],
                    ['MacBook Air M2', 'macbook-air-m2', 'Apple MacBook Air with M2', 'Lightweight laptop', 20000000, 'MBA-M2-001']
                ];
                
                foreach ($products as $product) {
                    $stmt = $pdo->prepare("
                        INSERT INTO products (name, slug, description, short_description, price, sku, stock_quantity, status, featured) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $product[0], $product[1], $product[2], $product[3], $product[4], $product[5], 10, 'active', 1
                    ]);
                }
                echo "<p class='success'>✅ Sample products created</p>";
            }
            
            // Get products for orders
            $stmt = $pdo->query("SELECT id, name, price FROM products LIMIT 3");
            $products = $stmt->fetchAll();
            
            if (!empty($products)) {
                $orders_data = [
                    [
                        'order_number' => 'AMB-' . date('Y') . '-' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                        'status' => 'delivered',
                        'payment_status' => 'paid',
                        'payment_method' => 'Bank Transfer BCA',
                        'total_amount' => $products[0]['price'],
                        'shipping_amount' => 25000,
                        'created_at' => date('Y-m-d H:i:s', strtotime('-7 days'))
                    ],
                    [
                        'order_number' => 'AMB-' . date('Y') . '-' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                        'status' => 'shipped',
                        'payment_status' => 'paid',
                        'payment_method' => 'GoPay',
                        'total_amount' => $products[1]['price'],
                        'shipping_amount' => 0,
                        'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                    ],
                    [
                        'order_number' => 'AMB-' . date('Y') . '-' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                        'status' => 'processing',
                        'payment_status' => 'paid',
                        'payment_method' => 'OVO',
                        'total_amount' => $products[2]['price'],
                        'shipping_amount' => 15000,
                        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                    ]
                ];
                
                foreach ($orders_data as $index => $order) {
                    $stmt = $pdo->prepare("
                        INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount, 
                                          payment_method, payment_status, shipping_address, billing_address, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $shipping_address = "Amos Baringbing\nJl. Sudirman No. 123\nJakarta Pusat, DKI Jakarta 10110\nPhone: ************";
                    
                    $stmt->execute([
                        $order['order_number'],
                        $user_id,
                        $order['status'],
                        $order['total_amount'],
                        $order['shipping_amount'],
                        $order['payment_method'],
                        $order['payment_status'],
                        $shipping_address,
                        $shipping_address,
                        $order['created_at']
                    ]);
                    
                    $order_id = $pdo->lastInsertId();
                    
                    // Add order item
                    $product = $products[$index];
                    $stmt = $pdo->prepare("
                        INSERT INTO order_items (order_id, product_id, quantity, price, total) 
                        VALUES (?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([$order_id, $product['id'], 1, $product['price'], $product['price']]);
                    
                    echo "<p class='success'>✅ Created order: {$order['order_number']} - {$order['status']}</p>";
                }
            }
        } else {
            echo "<p class='success'>✅ Amos already has $order_count orders</p>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Test My Orders Access</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div class='alert alert-success'>";
        echo "<h5><i class='fas fa-check-circle me-2'></i>Ready to Test!</h5>";
        echo "<p>Amos Baringbing is now logged in and should be able to access My Orders page.</p>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<div class='alert alert-success text-center'>";
    echo "<h3>🎉 Fix Complete!</h3>";
    echo "<p>Amos Baringbing account is now set up and logged in. You can access My Orders page.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<h4>❌ Error</h4>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-5'>";
echo "<h4 class='mb-4'>Test Your My Orders Page Now!</h4>";
echo "<div class='d-flex justify-content-center gap-3 flex-wrap'>";
echo "<a href='my-orders.php' class='btn btn-success btn-lg'><i class='fas fa-shopping-bag me-2'></i>My Orders Page</a>";
echo "<a href='debug-session.php' class='btn btn-info btn-lg'><i class='fas fa-bug me-2'></i>Debug Session</a>";
echo "<a href='my-orders-working.php' class='btn btn-primary btn-lg'><i class='fas fa-rocket me-2'></i>Working Version</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
