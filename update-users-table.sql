-- Add Firebase UID column to users table if it doesn't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS firebase_uid VARCHAR(255) UNIQUE;

-- Add index for Firebase UID
CREATE INDEX IF NOT EXISTS idx_firebase_uid ON users(firebase_uid);

-- Update existing users table structure if needed
ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NULL;

-- Insert demo Firebase users if they don't exist
INSERT IGNORE INTO users (email, first_name, last_name, firebase_uid, status, created_at) VALUES
('<EMAIL>', 'Test', 'User', NULL, 'active', NOW()),
('<EMAIL>', 'John', 'Doe', NULL, 'active', NOW()),
('<EMAIL>', 'Admin', 'User', NULL, 'active', NOW());
