<?php
require_once 'config/config.php';

$page_title = 'My Orders - Working Version';
$page_description = 'Working version of My Orders page';

// Auto-login if not logged in (for testing)
if (!isset($_SESSION['user_id'])) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['user_email'] = $user['email'];
        }
    } catch (Exception $e) {
        // Handle error
    }
}

// If still not logged in, show error
if (!isset($_SESSION['user_id'])) {
    include 'includes/header.php';
    echo "<div class='container py-5'>";
    echo "<div class='alert alert-danger text-center'>";
    echo "<h4>❌ Cannot Access My Orders</h4>";
    echo "<p>Please run the complete fix first:</p>";
    echo "<a href='complete-fix-my-orders.php' class='btn btn-warning btn-lg'>Complete Fix</a>";
    echo "</div>";
    echo "</div>";
    include 'includes/footer.php';
    exit;
}

$user_id = $_SESSION['user_id'];

// Get orders
$orders = [];
$error_message = '';

try {
    $pdo = getDBConnection();
    
    // Simple query first
    $sql = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$user_id]);
    $raw_orders = $stmt->fetchAll();
    
    // Enhanced query with product info
    foreach ($raw_orders as $order) {
        // Get order items
        $items_sql = "
            SELECT oi.*, p.name as product_name 
            FROM order_items oi 
            LEFT JOIN products p ON oi.product_id = p.id 
            WHERE oi.order_id = ?
        ";
        $items_stmt = $pdo->prepare($items_sql);
        $items_stmt->execute([$order['id']]);
        $items = $items_stmt->fetchAll();
        
        $order['item_count'] = count($items);
        $product_names = [];
        foreach ($items as $item) {
            $product_names[] = $item['product_name'] ?? 'Unknown Product';
        }
        $order['product_names'] = implode(', ', $product_names);
        
        $orders[] = $order;
    }
    
} catch (Exception $e) {
    $error_message = 'Error loading orders: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <!-- Success Banner -->
    <div class="alert alert-success">
        <h5 class="alert-heading">
            <i class="fas fa-check-circle me-2"></i>My Orders - Working Version!
        </h5>
        <p class="mb-2">Successfully logged in as: <strong><?php echo htmlspecialchars($_SESSION['user_name']); ?></strong></p>
        <p class="mb-0">This is a simplified working version of the My Orders page.</p>
    </div>

    <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2 mb-1 text-primary">
                <i class="fas fa-shopping-bag me-2"></i>My Orders
            </h1>
            <p class="text-muted">Track and manage your order history</p>
        </div>
        <div class="text-end">
            <span class="badge bg-primary fs-6 mb-2"><?php echo count($orders); ?> Orders</span>
            <br>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-plus me-1"></i>Shop More
            </a>
        </div>
    </div>

    <!-- Orders List -->
    <?php if (empty($orders)): ?>
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 120px; height: 120px;">
                        <i class="fas fa-shopping-bag fa-3x text-muted"></i>
                    </div>
                </div>
                <h3 class="text-gray-900 mb-3">No Orders Found</h3>
                <p class="text-muted mb-4 lead">
                    You haven't placed any orders yet. Start shopping to see your orders here!
                </p>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>Start Shopping
                    </a>
                    <a href="<?php echo SITE_URL; ?>/complete-fix-my-orders.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-wrench me-2"></i>Create Sample Orders
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="row">
            <?php foreach ($orders as $order): ?>
                <?php
                $status_colors = [
                    'pending' => 'warning',
                    'processing' => 'info',
                    'shipped' => 'primary',
                    'delivered' => 'success',
                    'cancelled' => 'danger'
                ];
                
                $payment_colors = [
                    'pending' => 'warning',
                    'paid' => 'success',
                    'failed' => 'danger',
                    'refunded' => 'secondary'
                ];
                
                $status_color = $status_colors[$order['status']] ?? 'secondary';
                $payment_color = $payment_colors[$order['payment_status']] ?? 'secondary';
                ?>
                <div class="col-lg-6 mb-4">
                    <div class="card h-100 shadow-sm border-0 hover-card">
                        <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1 text-primary fw-bold">
                                    <i class="fas fa-receipt me-2"></i>
                                    <?php echo htmlspecialchars($order['order_number']); ?>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo formatDate($order['created_at']); ?>
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php echo $status_color; ?> mb-1 px-3 py-2">
                                    <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                                    <?php echo ucfirst($order['status']); ?>
                                </span>
                                <br>
                                <span class="badge bg-<?php echo $payment_color; ?> px-3 py-1">
                                    <i class="fas fa-credit-card me-1"></i>
                                    <?php echo ucfirst($order['payment_status']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <small class="text-muted">Items</small>
                                    <div class="fw-bold"><?php echo $order['item_count']; ?> item(s)</div>
                                </div>
                                <div class="col-6 text-end">
                                    <small class="text-muted">Total</small>
                                    <div class="fw-bold text-success"><?php echo formatPrice($order['total_amount']); ?></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">Products</small>
                                <div class="small">
                                    <?php 
                                    $product_names = $order['product_names'] ?? 'No products';
                                    echo strlen($product_names) > 60 ? substr($product_names, 0, 60) . '...' : $product_names;
                                    ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($order['payment_method'])): ?>
                            <div class="mb-3">
                                <small class="text-muted">Payment Method</small>
                                <div class="small"><?php echo htmlspecialchars($order['payment_method']); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    <?php echo timeAgo($order['created_at']); ?>
                                </small>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="alert('Order details: <?php echo $order['order_number']; ?>')">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </button>
                                    <?php if ($order['status'] === 'pending'): ?>
                                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="alert('Cancel order feature coming soon!')">
                                            <i class="fas fa-times me-1"></i>Cancel
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="text-center mt-5">
        <h4 class="mb-4">Quick Actions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/my-orders.php" class="btn btn-primary">
                <i class="fas fa-shopping-bag me-2"></i>Original My Orders
            </a>
            <a href="<?php echo SITE_URL; ?>/complete-fix-my-orders.php" class="btn btn-warning">
                <i class="fas fa-wrench me-2"></i>Complete Fix
            </a>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-success">
                <i class="fas fa-shopping-cart me-2"></i>Shop Products
            </a>
            <a href="<?php echo SITE_URL; ?>/logout.php" class="btn btn-outline-secondary">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>
</div>

<style>
.hover-card {
    transition: all 0.3s ease;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.text-gray-900 {
    color: #1a202c !important;
}
</style>

<?php include 'includes/footer.php'; ?>
