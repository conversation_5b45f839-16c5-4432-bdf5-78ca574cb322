<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Firebase Users - TeWuNeed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-fire me-2"></i>Firebase Users Setup</h4>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">This page will create demo users in Firebase Authentication so you can test the login system.</p>
                        
                        <div id="status-container"></div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Test User</h6>
                                        <code><EMAIL></code><br>
                                        <code>user123</code>
                                        <br><br>
                                        <button class="btn btn-primary btn-sm" onclick="createUser('<EMAIL>', 'user123', 'Test', 'User')">
                                            <i class="fas fa-plus me-1"></i>Create
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>John Doe</h6>
                                        <code><EMAIL></code><br>
                                        <code>john123</code>
                                        <br><br>
                                        <button class="btn btn-primary btn-sm" onclick="createUser('<EMAIL>', 'john123', 'John', 'Doe')">
                                            <i class="fas fa-plus me-1"></i>Create
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>Admin User</h6>
                                        <code><EMAIL></code><br>
                                        <code>admin123</code>
                                        <br><br>
                                        <button class="btn btn-primary btn-sm" onclick="createUser('<EMAIL>', 'admin123', 'Admin', 'User')">
                                            <i class="fas fa-plus me-1"></i>Create
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <button class="btn btn-success btn-lg" onclick="createAllUsers()">
                                <i class="fas fa-users me-2"></i>Create All Demo Users
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="text-center">
                            <h5>Test Login</h5>
                            <p class="text-muted">After creating users, test the login system:</p>
                            <a href="login.php" class="btn btn-outline-primary me-2">
                                <i class="fas fa-sign-in-alt me-1"></i>Go to Login
                            </a>
                            <a href="register.php" class="btn btn-outline-success">
                                <i class="fas fa-user-plus me-1"></i>Go to Register
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { 
            getAuth, 
            createUserWithEmailAndPassword,
            updateProfile
        } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDz5t6mlBzXq7bjK3PGOGyEBo_WzjsHYME",
            authDomain: "tewuneed-marketplace.firebaseapp.com",
            databaseURL: "https://tewuneed-marketplace-default-rtdb.asia-southeast1.firebasedatabase.app",
            projectId: "tewuneed-marketplace",
            storageBucket: "tewuneed-marketplace.firebasestorage.app",
            messagingSenderId: "999093621738",
            appId: "1:999093621738:web:87b68aa3a5a5ebca395893",
            measurementId: "G-8WNLD8T7GY"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);

        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle';
            
            container.innerHTML += `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${icon} me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        async function createFirebaseUser(email, password, firstName, lastName) {
            try {
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;
                
                // Update user profile
                await updateProfile(user, {
                    displayName: `${firstName} ${lastName}`
                });
                
                showStatus(`✓ Created Firebase user: ${email}`, 'success');
                return { success: true, user };
            } catch (error) {
                if (error.code === 'auth/email-already-in-use') {
                    showStatus(`⚠ User ${email} already exists in Firebase`, 'info');
                    return { success: true, exists: true };
                } else {
                    showStatus(`✗ Failed to create ${email}: ${error.message}`, 'error');
                    return { success: false, error: error.message };
                }
            }
        }

        // Make functions global
        window.createUser = async function(email, password, firstName, lastName) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';
            
            await createFirebaseUser(email, password, firstName, lastName);
            
            button.disabled = false;
            button.innerHTML = originalText;
        };

        window.createAllUsers = async function() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating All Users...';
            
            showStatus('Starting to create all demo users...', 'info');
            
            const users = [
                { email: '<EMAIL>', password: 'user123', firstName: 'Test', lastName: 'User' },
                { email: '<EMAIL>', password: 'john123', firstName: 'John', lastName: 'Doe' },
                { email: '<EMAIL>', password: 'admin123', firstName: 'Admin', lastName: 'User' }
            ];
            
            for (const user of users) {
                await createFirebaseUser(user.email, user.password, user.firstName, user.lastName);
                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            showStatus('✅ All demo users setup complete! You can now test the login system.', 'success');
            
            button.disabled = false;
            button.innerHTML = originalText;
        };
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
