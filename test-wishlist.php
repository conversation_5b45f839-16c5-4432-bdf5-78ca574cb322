<?php
require_once 'config/config.php';

$page_title = 'Test Wishlist System';
$page_description = 'Testing the wishlist functionality';

// Auto-login for testing
if (!isset($_SESSION['user_id'])) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute(['<EMAIL>']);
        $user = $stmt->fetch();
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['user_email'] = $user['email'];
        }
    } catch (Exception $e) {
        // Handle error
    }
}

// Get some products for testing
$products = [];
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active' 
        LIMIT 6
    ");
    $products = $stmt->fetchAll();
} catch (Exception $e) {
    $error_message = 'Error loading products: ' . $e->getMessage();
}

include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-primary">💝 Test Wishlist System</h1>
        <p class="lead text-gray-600">Test adding and removing products from your wishlist</p>
    </div>

    <!-- Login Status -->
    <?php if (isset($_SESSION['user_id'])): ?>
        <div class="alert alert-success">
            <h5 class="alert-heading">
                <i class="fas fa-check-circle me-2"></i>Logged In Successfully!
            </h5>
            <p class="mb-2">User: <strong><?php echo htmlspecialchars($_SESSION['user_name']); ?></strong></p>
            <p class="mb-0">You can now test the wishlist functionality.</p>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>Not Logged In
            </h5>
            <p class="mb-3">You need to be logged in to test the wishlist.</p>
            <a href="<?php echo SITE_URL; ?>/setup-wishlist.php" class="btn btn-warning">Setup & Auto Login</a>
        </div>
    <?php endif; ?>

    <!-- Instructions -->
    <div class="card border-0 shadow-sm mb-5">
        <div class="card-header bg-info text-white">
            <h4 class="mb-0">🧪 How to Test Wishlist</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Testing Steps</h6>
                    <ol class="small">
                        <li>Click the heart icon on any product below</li>
                        <li>Watch the heart turn red when added to wishlist</li>
                        <li>Check the wishlist count in the header menu</li>
                        <li>Click the heart again to remove from wishlist</li>
                        <li>Visit the wishlist page to see all saved items</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">Features to Test</h6>
                    <ul class="small">
                        <li>Add/remove products from wishlist</li>
                        <li>Wishlist count updates in real-time</li>
                        <li>Toast notifications for actions</li>
                        <li>Wishlist page displays saved items</li>
                        <li>Remove items from wishlist page</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Products -->
    <?php if (!empty($products)): ?>
        <h3 class="mb-4">🛍️ Test Products</h3>
        <div class="row">
            <?php foreach ($products as $product): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="position-relative">
                            <?php if ($product['image']): ?>
                                <img src="<?php echo SITE_URL; ?>/uploads/<?php echo $product['image']; ?>" 
                                     class="card-img-top" alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     style="height: 200px; object-fit: cover;">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Wishlist Button -->
                            <button class="btn btn-outline-danger wishlist-btn position-absolute" 
                                    style="top: 10px; right: 10px; border-radius: 50%; width: 40px; height: 40px;"
                                    data-product-id="<?php echo $product['id']; ?>"
                                    title="Add to Wishlist">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <?php if ($product['category_name']): ?>
                                    <span class="badge bg-light text-dark small"><?php echo htmlspecialchars($product['category_name']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <h5 class="card-title fw-bold mb-2">
                                <?php echo htmlspecialchars($product['name']); ?>
                            </h5>
                            
                            <?php if ($product['short_description']): ?>
                                <p class="card-text text-muted small mb-3">
                                    <?php echo htmlspecialchars(substr($product['short_description'], 0, 100)) . '...'; ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <?php if ($product['sale_price']): ?>
                                    <span class="h5 fw-bold text-danger me-2"><?php echo formatPrice($product['sale_price']); ?></span>
                                    <span class="text-muted text-decoration-line-through"><?php echo formatPrice($product['price']); ?></span>
                                <?php else: ?>
                                    <span class="h5 fw-bold text-primary"><?php echo formatPrice($product['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mt-auto">
                                <div class="d-flex gap-2">
                                    <a href="<?php echo SITE_URL; ?>/product-detail.php?slug=<?php echo $product['slug']; ?>" 
                                       class="btn btn-primary flex-fill">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <button class="btn btn-success">
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>No Products Found
            </h5>
            <p class="mb-3">No products available for testing. Please add some products first.</p>
            <a href="<?php echo SITE_URL; ?>/setup-wishlist.php" class="btn btn-warning">Setup Products</a>
        </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="text-center mt-5">
        <h4 class="mb-4">Quick Actions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="<?php echo SITE_URL; ?>/wishlist.php" class="btn btn-primary btn-lg">
                <i class="fas fa-heart me-2"></i>View Wishlist
            </a>
            <a href="<?php echo SITE_URL; ?>/setup-wishlist.php" class="btn btn-warning btn-lg">
                <i class="fas fa-wrench me-2"></i>Setup Wishlist
            </a>
            <a href="<?php echo SITE_URL; ?>/products.php" class="btn btn-success btn-lg">
                <i class="fas fa-shopping-bag me-2"></i>Browse Products
            </a>
        </div>
    </div>

    <!-- Test Results -->
    <div class="card border-0 shadow-sm mt-5">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">✅ Expected Test Results</h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">When Adding to Wishlist</h6>
                    <ul class="small">
                        <li>Heart icon turns red</li>
                        <li>Success toast notification appears</li>
                        <li>Wishlist count in header increases</li>
                        <li>Button tooltip changes to "Remove from Wishlist"</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold mb-3">When Removing from Wishlist</h6>
                    <ul class="small">
                        <li>Heart icon becomes outline</li>
                        <li>Success toast notification appears</li>
                        <li>Wishlist count in header decreases</li>
                        <li>Item removed from wishlist page</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.wishlist-btn.in-wishlist {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.text-gray-600 {
    color: #6b7280 !important;
}
</style>

<?php include 'includes/footer.php'; ?>
