<?php
// Fix Orders Table Structure
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 Fix Orders Table Structure</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 900px; margin: 20px auto; padding: 20px;'>";

try {
    // Database configuration
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'db_tewuneed2';
    
    echo "<h2>Step 1: Database Connection</h2>";
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Connected to database: $database</p>";
    
    echo "<h2>Step 2: Check Current Orders Table Structure</h2>";
    $stmt = $pdo->query("DESCRIBE orders");
    $columns = $stmt->fetchAll();
    
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "<p>• {$column['Field']} - {$column['Type']}</p>";
    }
    
    echo "<h2>Step 3: Add Missing Columns</h2>";
    
    // Add coupon_id column if it doesn't exist
    if (!in_array('coupon_id', $existing_columns)) {
        $pdo->exec("ALTER TABLE orders ADD COLUMN coupon_id INT NULL AFTER notes");
        echo "<p style='color: green;'>✓ Added coupon_id column</p>";
    } else {
        echo "<p style='color: blue;'>• coupon_id column already exists</p>";
    }
    
    // Add customer_info column if it doesn't exist
    if (!in_array('customer_info', $existing_columns)) {
        $pdo->exec("ALTER TABLE orders ADD COLUMN customer_info TEXT NULL AFTER coupon_id");
        echo "<p style='color: green;'>✓ Added customer_info column</p>";
    } else {
        echo "<p style='color: blue;'>• customer_info column already exists</p>";
    }
    
    echo "<h2>Step 4: Verify Updated Structure</h2>";
    $stmt = $pdo->query("DESCRIBE orders");
    $updated_columns = $stmt->fetchAll();
    
    foreach ($updated_columns as $column) {
        echo "<p>• {$column['Field']} - {$column['Type']}</p>";
    }
    
    echo "<h2>✅ Orders Table Structure Fixed!</h2>";
    echo "<p style='color: green; font-weight: bold;'>The orders table now has all required columns for the checkout process.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "</div>";
?>
