<?php
require_once 'config/config.php';

$page_title = 'Firebase Test';
$page_description = 'Test Firebase Authentication';

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">🔥 Firebase Authentication Test</h2>
                    
                    <div id="auth-status" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>Checking Firebase connection...
                    </div>
                    
                    <div id="user-info" style="display: none;">
                        <h4>Current User:</h4>
                        <div id="user-details"></div>
                        <button id="logoutBtn" class="btn btn-warning mt-3">Logout</button>
                    </div>
                    
                    <div id="login-section">
                        <h4>Quick Login Test</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Test Login</h6>
                                        <form id="testLoginForm">
                                            <div class="mb-2">
                                                <input type="email" class="form-control" id="loginEmail" 
                                                       placeholder="Email" value="<EMAIL>">
                                            </div>
                                            <div class="mb-2">
                                                <input type="password" class="form-control" id="loginPassword" 
                                                       placeholder="Password" value="user123">
                                            </div>
                                            <button type="submit" class="btn btn-primary">Test Login</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6>Create Test Account</h6>
                                        <button class="btn btn-success btn-sm mb-2 w-100" 
                                                onclick="createTestAccount('<EMAIL>')">
                                            Create <EMAIL>
                                        </button>
                                        <button class="btn btn-success btn-sm mb-2 w-100" 
                                                onclick="createTestAccount('<EMAIL>')">
                                            Create <EMAIL>
                                        </button>
                                        <button class="btn btn-success btn-sm w-100" 
                                                onclick="createTestAccount('<EMAIL>')">
                                            Create <EMAIL>
                                        </button>
                                        <small class="text-muted">Password: user123</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="test-results" class="mt-4"></div>
                    
                    <div class="mt-4">
                        <h5>Navigation Links:</h5>
                        <a href="login_firebase.php" class="btn btn-outline-primary me-2">Firebase Login</a>
                        <a href="firebase_register.php" class="btn btn-outline-success me-2">Firebase Register</a>
                        <a href="index.php" class="btn btn-outline-secondary">Home Page</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="module">
// Import Firebase modules
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { 
    getAuth, 
    signInWithEmailAndPassword,
    createUserWithEmailAndPassword,
    onAuthStateChanged,
    signOut
} from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDz5t6mlBzXq7bjK3PGOGyEBo_WzjsHYME",
    authDomain: "tewuneed-marketplace.firebaseapp.com",
    databaseURL: "https://tewuneed-marketplace-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "tewuneed-marketplace",
    storageBucket: "tewuneed-marketplace.firebasestorage.app",
    messagingSenderId: "999093621738",
    appId: "1:999093621738:web:87b68aa3a5a5ebca395893",
    measurementId: "G-8WNLD8T7GY"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

console.log('Firebase initialized successfully');

// Helper functions
function showResult(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    const testResults = document.getElementById('test-results');
    testResults.innerHTML += `
        <div class="alert ${alertClass} alert-dismissible fade show">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

// Create test account function
window.createTestAccount = async function(email) {
    try {
        showResult(`Creating account for ${email}...`, 'info');
        const userCredential = await createUserWithEmailAndPassword(auth, email, 'user123');
        showResult(`✅ Account created successfully: ${userCredential.user.email}`, 'success');
    } catch (error) {
        if (error.code === 'auth/email-already-in-use') {
            showResult(`ℹ️ Account ${email} already exists. You can login with it.`, 'info');
        } else {
            showResult(`❌ Failed to create ${email}: ${error.message}`, 'error');
        }
    }
};

// Listen for auth state changes
onAuthStateChanged(auth, (user) => {
    const authStatus = document.getElementById('auth-status');
    const userInfo = document.getElementById('user-info');
    const userDetails = document.getElementById('user-details');
    const loginSection = document.getElementById('login-section');
    
    if (user) {
        authStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i>Firebase connected - User logged in';
        authStatus.className = 'alert alert-success';
        
        userDetails.innerHTML = `
            <p><strong>Email:</strong> ${user.email}</p>
            <p><strong>UID:</strong> ${user.uid}</p>
            <p><strong>Email Verified:</strong> ${user.emailVerified}</p>
            <p><strong>Created:</strong> ${new Date(user.metadata.creationTime).toLocaleString()}</p>
        `;
        
        userInfo.style.display = 'block';
        loginSection.style.display = 'none';
        
        showResult(`🎉 User ${user.email} is logged in!`, 'success');
    } else {
        authStatus.innerHTML = '<i class="fas fa-wifi me-2"></i>Firebase connected - No user logged in';
        authStatus.className = 'alert alert-warning';
        
        userInfo.style.display = 'none';
        loginSection.style.display = 'block';
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Test login form
    document.getElementById('testLoginForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;
        
        showResult(`Testing login for ${email}...`, 'info');
        
        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            showResult(`✅ Login successful: ${userCredential.user.email}`, 'success');
            
            // Test redirect to home page
            setTimeout(() => {
                showResult('🏠 Redirecting to home page...', 'info');
                window.location.href = 'index.php';
            }, 2000);
            
        } catch (error) {
            showResult(`❌ Login failed: ${error.message}`, 'error');
        }
    });
    
    // Logout button
    document.getElementById('logoutBtn').addEventListener('click', async () => {
        try {
            await signOut(auth);
            showResult('✅ Logged out successfully', 'success');
        } catch (error) {
            showResult(`❌ Logout failed: ${error.message}`, 'error');
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
