<?php
require_once 'config/config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix My Orders Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        h1, h2 { color: #333; }
        .step { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔧 Fix My Orders Page</h1>";

try {
    $pdo = getDBConnection();
    
    echo "<div class='step'>";
    echo "<h2>Step 1: Check Database Tables</h2>";
    
    // Check if orders table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'orders'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Orders table exists</p>";
    } else {
        echo "<p class='error'>❌ Orders table missing</p>";
        // Create orders table
        $pdo->exec("
            CREATE TABLE orders (
                id INT PRIMARY KEY AUTO_INCREMENT,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                user_id INT,
                status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
                total_amount DECIMAL(10,2) NOT NULL,
                shipping_amount DECIMAL(10,2) DEFAULT 0,
                tax_amount DECIMAL(10,2) DEFAULT 0,
                discount_amount DECIMAL(10,2) DEFAULT 0,
                payment_method VARCHAR(50),
                payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
                shipping_address TEXT,
                billing_address TEXT,
                notes TEXT,
                shipped_at TIMESTAMP NULL,
                delivered_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_user (user_id),
                INDEX idx_status (status),
                INDEX idx_order_number (order_number)
            )
        ");
        echo "<p class='success'>✅ Orders table created</p>";
    }
    
    // Check if order_items table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'order_items'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Order items table exists</p>";
    } else {
        echo "<p class='error'>❌ Order items table missing</p>";
        // Create order_items table
        $pdo->exec("
            CREATE TABLE order_items (
                id INT PRIMARY KEY AUTO_INCREMENT,
                order_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                total DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                INDEX idx_order (order_id),
                INDEX idx_product (product_id)
            )
        ");
        echo "<p class='success'>✅ Order items table created</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Check for Test User</h2>";
    
    // Check if we have a test user
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $user_count = $stmt->fetchColumn();
    
    if ($user_count == 0) {
        echo "<p class='warning'>⚠️ No users found, creating test user</p>";
        // Create test user
        $password = password_hash('password123', PASSWORD_DEFAULT);
        $pdo->exec("
            INSERT INTO users (email, password, first_name, last_name, phone, status) 
            VALUES ('<EMAIL>', '$password', 'Test', 'User', '081234567890', 'active')
        ");
        echo "<p class='success'>✅ Test user created (email: <EMAIL>, password: password123)</p>";
    } else {
        echo "<p class='success'>✅ Found $user_count users in database</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Check for Products</h2>";
    
    // Check if we have products
    $stmt = $pdo->query("SELECT COUNT(*) FROM products");
    $product_count = $stmt->fetchColumn();
    
    if ($product_count == 0) {
        echo "<p class='warning'>⚠️ No products found, creating sample products</p>";
        // Create sample products
        $pdo->exec("
            INSERT INTO products (name, slug, description, short_description, price, sku, stock_quantity, status, featured) VALUES
            ('Samsung Galaxy S24', 'samsung-galaxy-s24', 'Latest Samsung flagship smartphone', 'Premium smartphone with advanced features', 15000000, 'SGS24-001', 10, 'active', 1),
            ('iPhone 15 Pro', 'iphone-15-pro', 'Apple iPhone 15 Pro', 'Latest iPhone with Pro features', 18000000, 'IP15P-001', 8, 'active', 1),
            ('MacBook Air M2', 'macbook-air-m2', 'Apple MacBook Air with M2 chip', 'Lightweight laptop for professionals', 20000000, 'MBA-M2-001', 5, 'active', 1)
        ");
        echo "<p class='success'>✅ Sample products created</p>";
    } else {
        echo "<p class='success'>✅ Found $product_count products in database</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Create Sample Orders</h2>";
    
    // Get first user ID
    $stmt = $pdo->query("SELECT id FROM users LIMIT 1");
    $user_id = $stmt->fetchColumn();
    
    // Get first few product IDs
    $stmt = $pdo->query("SELECT id, name, price FROM products LIMIT 3");
    $products = $stmt->fetchAll();
    
    if ($user_id && !empty($products)) {
        // Check if orders already exist
        $stmt = $pdo->query("SELECT COUNT(*) FROM orders WHERE user_id = $user_id");
        $order_count = $stmt->fetchColumn();
        
        if ($order_count == 0) {
            echo "<p class='info'>Creating sample orders for user ID: $user_id</p>";
            
            // Create sample orders
            $orders_data = [
                [
                    'order_number' => 'TWN-' . date('Y') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
                    'status' => 'delivered',
                    'payment_status' => 'paid',
                    'payment_method' => 'Bank Transfer BCA',
                    'total_amount' => $products[0]['price'],
                    'shipping_amount' => 25000,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-7 days'))
                ],
                [
                    'order_number' => 'TWN-' . date('Y') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
                    'status' => 'shipped',
                    'payment_status' => 'paid',
                    'payment_method' => 'GoPay',
                    'total_amount' => $products[1]['price'],
                    'shipping_amount' => 0,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                ],
                [
                    'order_number' => 'TWN-' . date('Y') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
                    'status' => 'processing',
                    'payment_status' => 'paid',
                    'payment_method' => 'OVO',
                    'total_amount' => $products[2]['price'],
                    'shipping_amount' => 15000,
                    'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                ]
            ];
            
            foreach ($orders_data as $index => $order) {
                $stmt = $pdo->prepare("
                    INSERT INTO orders (order_number, user_id, status, total_amount, shipping_amount, 
                                      payment_method, payment_status, shipping_address, billing_address, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $shipping_address = "Test User\nJl. Sudirman No. 123\nJakarta Pusat, DKI Jakarta 10110\nPhone: 081234567890";
                
                $stmt->execute([
                    $order['order_number'],
                    $user_id,
                    $order['status'],
                    $order['total_amount'],
                    $order['shipping_amount'],
                    $order['payment_method'],
                    $order['payment_status'],
                    $shipping_address,
                    $shipping_address,
                    $order['created_at']
                ]);
                
                $order_id = $pdo->lastInsertId();
                
                // Add order item
                $product = $products[$index];
                $stmt = $pdo->prepare("
                    INSERT INTO order_items (order_id, product_id, quantity, price, total) 
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$order_id, $product['id'], 1, $product['price'], $product['price']]);
                
                echo "<p class='success'>✅ Created order: {$order['order_number']} - {$order['status']}</p>";
            }
        } else {
            echo "<p class='info'>ℹ️ User already has $order_count orders</p>";
        }
    } else {
        echo "<p class='error'>❌ Cannot create orders: missing user or products</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Test My Orders Page</h2>";
    
    // Test the query from my-orders.php
    $test_user_id = $user_id;
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as item_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.user_id = ?
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$test_user_id]);
    $test_orders = $stmt->fetchAll();
    
    if (!empty($test_orders)) {
        echo "<p class='success'>✅ Query test successful! Found " . count($test_orders) . " orders</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 10px;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th style='padding: 8px;'>Order Number</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Total</th>";
        echo "<th style='padding: 8px;'>Items</th>";
        echo "<th style='padding: 8px;'>Products</th>";
        echo "</tr>";
        
        foreach ($test_orders as $order) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['status']) . "</td>";
            echo "<td style='padding: 8px;'>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td style='padding: 8px;'>" . $order['item_count'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($order['product_names']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ Query test returned no results</p>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>✅ Fix Complete!</h2>";
    echo "<p class='success'>Your My Orders page should now be working properly.</p>";
    echo "<p><strong>Test Instructions:</strong></p>";
    echo "<ol>";
    echo "<li>Login with: <strong><EMAIL></strong> / <strong>password123</strong></li>";
    echo "<li>Go to: <a href='my-orders.php' target='_blank'>My Orders Page</a></li>";
    echo "<li>You should see the sample orders listed</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step'>";
    echo "<h2 class='error'>❌ Error</h2>";
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='margin-top: 30px; text-align: center;'>";
echo "<a href='my-orders.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🛍️ Test My Orders</a>";
echo "<a href='login.php' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔐 Login Page</a>";
echo "<a href='index.php' style='background: #ffc107; color: black; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 Home Page</a>";
echo "</div>";

echo "</body></html>";
?>
