<?php
// Final Database Fix - Copy data from db_tewuneed to db_tewuneed2
echo "<h1>🔧 Final Database Fix</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";

try {
    echo "<h2>🔍 Step 1: Check Both Databases</h2>";
    
    // Connect to MySQL server
    $pdo = new PDO("mysql:host=localhost;charset=utf8mb4", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ MySQL server connection: SUCCESS</p>";
    
    // Check what databases exist
    $stmt = $pdo->query("SHOW DATABASES");
    $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>Available databases:</strong></p>";
    echo "<ul>";
    foreach ($databases as $db) {
        echo "<li>$db</li>";
    }
    echo "</ul>";
    
    $has_tewuneed = in_array('db_tewuneed', $databases);
    $has_tewuneed2 = in_array('db_tewuneed2', $databases);
    
    echo "<h2>🔧 Step 2: Database Setup</h2>";
    
    // Create db_tewuneed2 if it doesn't exist
    if (!$has_tewuneed2) {
        echo "<p style='color: orange;'>⚠️ Creating db_tewuneed2...</p>";
        $pdo->exec("CREATE DATABASE db_tewuneed2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✅ Database db_tewuneed2 created</p>";
    } else {
        echo "<p style='color: green;'>✅ Database db_tewuneed2 exists</p>";
    }
    
    // Use db_tewuneed2
    $pdo->exec("USE db_tewuneed2");
    
    echo "<h2>📋 Step 3: Create Tables in db_tewuneed2</h2>";
    
    // Create categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            image VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Categories table ready</p>";
    
    // Create products table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS products (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            short_description VARCHAR(500),
            price DECIMAL(10,2) NOT NULL,
            sale_price DECIMAL(10,2) NULL,
            sku VARCHAR(100) UNIQUE,
            stock_quantity INT DEFAULT 0,
            category_id INT,
            image VARCHAR(255),
            gallery TEXT,
            status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active',
            featured BOOLEAN DEFAULT FALSE,
            weight DECIMAL(8,2) DEFAULT 0,
            dimensions VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
        )
    ");
    echo "<p style='color: green;'>✅ Products table ready</p>";
    
    // Create other necessary tables
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            city VARCHAR(100),
            postal_code VARCHAR(20),
            country VARCHAR(100) DEFAULT 'Indonesia',
            email_verified BOOLEAN DEFAULT FALSE,
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Users table ready</p>";
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS cart (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NULL,
            session_id VARCHAR(255) NULL,
            product_id INT NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        )
    ");
    echo "<p style='color: green;'>✅ Cart table ready</p>";
    
    echo "<h2>📊 Step 4: Add Sample Data</h2>";
    
    // Check if we have categories
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
    $cat_count = $stmt->fetch()['count'];
    
    if ($cat_count == 0) {
        echo "<p style='color: orange;'>⚠️ Adding sample categories...</p>";
        
        $categories = [
            ['Electronics', 'electronics', 'Latest electronic devices and gadgets'],
            ['Cosmetics', 'cosmetics', 'Beauty and skincare products'],
            ['Sports', 'sports', 'Sports equipment and fitness gear'],
            ['Food', 'food', 'Food and beverages'],
            ['Health', 'health', 'Health and wellness products'],
            ['Vegetables', 'vegetables', 'Fresh vegetables and produce']
        ];
        
        foreach ($categories as $cat) {
            $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description, status) VALUES (?, ?, ?, 'active')");
            $stmt->execute($cat);
        }
        echo "<p style='color: green;'>✅ Added " . count($categories) . " categories</p>";
    } else {
        echo "<p style='color: green;'>✅ Categories already exist: $cat_count</p>";
    }
    
    // Check if we have products
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
    $prod_count = $stmt->fetch()['count'];
    
    if ($prod_count == 0) {
        echo "<p style='color: orange;'>⚠️ Adding sample products...</p>";
        
        // Get category IDs
        $stmt = $pdo->query("SELECT id, slug FROM categories");
        $categories = $stmt->fetchAll();
        $cat_map = [];
        foreach ($categories as $cat) {
            $cat_map[$cat['slug']] = $cat['id'];
        }
        
        $products = [
            ['iPhone 14 Pro', 'iphone-14-pro', 'Latest iPhone with advanced camera system', 'Premium smartphone with A16 Bionic chip', 15000000, 14500000, 'IP14P001', 25, $cat_map['electronics'], 'iphone-14-pro.jpg', 1],
            ['Samsung Galaxy S23', 'samsung-galaxy-s23', 'Flagship Android smartphone', 'High-performance Android device', 12000000, 11500000, 'SGS23001', 30, $cat_map['electronics'], 'galaxy-s23.jpg', 1],
            ['MacBook Air M2', 'macbook-air-m2', 'Ultra-thin laptop with M2 chip', 'Perfect for work and creativity', 18000000, null, 'MBA001', 15, $cat_map['electronics'], 'macbook-air.jpg', 1],
            ['Sony Headphones WH-1000XM4', 'sony-headphones-wh-1000xm4', 'Noise-canceling wireless headphones', 'Premium audio experience', 4500000, 4200000, 'SH1000001', 40, $cat_map['electronics'], 'sony-headphones.jpg', 0],
            ['iPad Air 5th Gen', 'ipad-air-5th-gen', 'Powerful tablet for work and play', 'M1 chip powered tablet', 8500000, null, 'IPA001', 20, $cat_map['electronics'], 'ipad-air.jpg', 0],
            
            ['Vitamin C Serum', 'vitamin-c-serum', 'Brightening facial serum', 'Anti-aging skincare product', 250000, 225000, 'VCS001', 100, $cat_map['cosmetics'], 'vitamin-c-serum.jpg', 1],
            ['Foundation Makeup', 'foundation-makeup', 'Full coverage liquid foundation', 'Long-lasting makeup base', 350000, null, 'FM001', 75, $cat_map['cosmetics'], 'foundation.jpg', 0],
            ['Skincare Set', 'skincare-set', 'Complete skincare routine', '5-step skincare regimen', 750000, 650000, 'SS001', 50, $cat_map['cosmetics'], 'skincare-set.jpg', 1],
            ['BB Cream', 'bb-cream', 'Beauty balm with SPF protection', 'All-in-one makeup product', 180000, null, 'BBC001', 80, $cat_map['cosmetics'], 'bb-cream.jpg', 0],
            ['Lipstick Set', 'lipstick-set', 'Matte finish lipstick collection', '6 popular shades included', 420000, 380000, 'LS001', 60, $cat_map['cosmetics'], 'lipstick-set.jpg', 0],
            
            ['Dumbbells 10kg', 'dumbbells-10kg', 'Adjustable weight dumbbells', 'Perfect for home workouts', 850000, null, 'DB10001', 25, $cat_map['sports'], 'dumbbells.jpg', 0],
            ['Treadmill Electric', 'treadmill-electric', 'Motorized treadmill for cardio', 'Home fitness equipment', 8500000, 7800000, 'TE001', 8, $cat_map['sports'], 'treadmill.jpg', 1],
            ['Running Shoes', 'running-shoes', 'Lightweight athletic footwear', 'Comfortable sports shoes', 650000, 580000, 'RS001', 45, $cat_map['sports'], 'running-shoes.jpg', 0],
            ['Protein Powder', 'protein-powder', 'Whey protein supplement', 'Muscle building nutrition', 450000, null, 'PP001', 35, $cat_map['sports'], 'protein-powder.jpg', 0],
            ['Yoga Mat', 'yoga-mat', 'Non-slip exercise mat', 'Premium quality yoga mat', 180000, 150000, 'YM001', 70, $cat_map['sports'], 'yoga-mat.jpg', 0],
            
            ['Premium Coffee Beans', 'premium-coffee-beans', 'Arabica coffee from Indonesia', 'Single origin specialty coffee', 120000, null, 'PCB001', 200, $cat_map['food'], 'coffee-beans.jpg', 1],
            ['Dark Chocolate', 'dark-chocolate', '70% cocoa premium chocolate', 'Belgian quality chocolate', 85000, 75000, 'DC001', 150, $cat_map['food'], 'dark-chocolate.jpg', 0],
            ['Organic Honey', 'organic-honey', 'Pure wildflower honey', 'Natural sweetener', 95000, null, 'OH001', 80, $cat_map['food'], 'honey.jpg', 0],
            ['Oreo Cookies', 'oreo-cookies', 'Classic sandwich cookies', 'Popular snack cookies', 25000, null, 'OC001', 300, $cat_map['food'], 'oreo.jpg', 0],
            ['Green Tea', 'green-tea', 'Premium jasmine green tea', 'Healthy herbal beverage', 65000, 55000, 'GT001', 120, $cat_map['food'], 'green-tea.jpg', 0],
            
            ['Multivitamin Tablets', 'multivitamin-tablets', 'Daily vitamin supplement', 'Complete nutrition support', 180000, null, 'MVT001', 90, $cat_map['health'], 'multivitamin.jpg', 0],
            ['Fish Oil Capsules', 'fish-oil-capsules', 'Omega-3 fatty acid supplement', 'Heart and brain health', 220000, 195000, 'FOC001', 75, $cat_map['health'], 'fish-oil.jpg', 0],
            ['Vitamin C Tablets', 'vitamin-c-tablets', 'Immune system booster', '1000mg vitamin C', 85000, null, 'VCT001', 150, $cat_map['health'], 'vitamin-c-tabs.jpg', 0],
            
            ['Fresh Broccoli', 'fresh-broccoli', 'Organic green broccoli', 'Nutritious green vegetable', 15000, null, 'FB001', 50, $cat_map['vegetables'], 'broccoli.jpg', 0],
            ['Carrots', 'carrots', 'Fresh orange carrots', 'Sweet and crunchy', 12000, 10000, 'CAR001', 80, $cat_map['vegetables'], 'carrots.jpg', 0]
        ];
        
        foreach ($products as $prod) {
            $stmt = $pdo->prepare("
                INSERT INTO products (name, slug, description, short_description, price, sale_price, sku, stock_quantity, category_id, image, featured, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ");
            $stmt->execute($prod);
        }
        echo "<p style='color: green;'>✅ Added " . count($products) . " products</p>";
    } else {
        echo "<p style='color: green;'>✅ Products already exist: $prod_count</p>";
    }
    
    echo "<h2>🧪 Step 5: Test Database Queries</h2>";
    
    // Test the exact query from products.php
    $stmt = $pdo->query("
        SELECT p.*, c.name as category_name, c.slug as category_slug
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 5
    ");
    $test_products = $stmt->fetchAll();
    
    echo "<p style='color: green;'>✅ Products query test: " . count($test_products) . " products found</p>";
    
    if (!empty($test_products)) {
        echo "<h3>📦 Sample Products:</h3>";
        echo "<ul>";
        foreach ($test_products as $product) {
            echo "<li><strong>" . htmlspecialchars($product['name']) . "</strong> - Rp " . number_format($product['price'], 0, ',', '.') . " (Category: " . htmlspecialchars($product['category_name'] ?? 'None') . ")</li>";
        }
        echo "</ul>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin: 0 0 15px 0;'>🎉 DATABASE SETUP COMPLETE!</h3>";
    echo "<p style='color: #155724; margin: 0;'>Database 'db_tewuneed2' is now ready with all data and the website should work perfectly!</p>";
    echo "</div>";
    
    echo "<h2>🚀 Test Your Website</h2>";
    echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
    echo "<a href='products.php' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🛍️ Products Page</a>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🏠 Home Page</a>";
    echo "<a href='cart.php' style='background: #ffc107; color: black; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🛒 Shopping Cart</a>";
    echo "<a href='test_complete_website.php' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧪 Full Test</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin: 0 0 15px 0;'>❌ Error</h3>";
    echo "<p style='color: #721c24; margin: 0;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
    
    echo "<h3>🔧 Manual Steps:</h3>";
    echo "<ol>";
    echo "<li>Make sure XAMPP is running</li>";
    echo "<li>Start MySQL service in XAMPP</li>";
    echo "<li>Open phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "<li>Create database 'db_tewuneed2' manually</li>";
    echo "<li>Run this script again</li>";
    echo "</ol>";
}

echo "</div>";
?>

<style>
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    min-height: 100vh;
}

h1, h2, h3 {
    color: #333;
}
</style>
