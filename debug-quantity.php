<?php
require_once 'config/config.php';

// Get a sample product
$product = null;
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT * FROM products WHERE status = 'active' AND stock_quantity > 0 LIMIT 1");
    $product = $stmt->fetch();
} catch (Exception $e) {
    $error_message = 'Error loading product: ' . $e->getMessage();
}

if (!$product) {
    $product = [
        'id' => 1,
        'name' => 'Test Product',
        'price' => 100000,
        'stock_quantity' => 10,
        'image' => null
    ];
}

$page_title = 'Debug Quantity Controls';
include 'includes/header.php';
?>

<div class="container-lg py-5">
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold text-danger">🐛 Debug Quantity Controls</h1>
        <p class="lead">Debugging the + and - buttons issue</p>
    </div>

    <!-- Debug Info -->
    <div class="alert alert-info mb-4">
        <h5>Debug Information</h5>
        <p><strong>Product ID:</strong> <?php echo $product['id']; ?></p>
        <p><strong>Stock Quantity:</strong> <?php echo $product['stock_quantity']; ?></p>
        <p><strong>Page URL:</strong> <?php echo $_SERVER['REQUEST_URI']; ?></p>
    </div>

    <!-- Exact same HTML structure as product-detail.php -->
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Quantity Controls Debug</h5>
                </div>
                <div class="card-body">
                    <!-- Add to Cart -->
                    <?php if ($product['stock_quantity'] > 0): ?>
                        <div class="add-to-cart mb-4">
                            <div class="row g-3 align-items-center">
                                <div class="col-auto">
                                    <label class="form-label mb-2">Quantity:</label>
                                    <div class="input-group" style="width: 140px;">
                                        <button class="btn btn-outline-secondary qty-btn" type="button" id="decrease-qty">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center" id="quantity" 
                                               value="1" min="1" max="<?php echo $product['stock_quantity']; ?>" 
                                               style="width: 60px; font-weight: bold;">
                                        <button class="btn btn-outline-secondary qty-btn" type="button" id="increase-qty">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    <small class="text-muted">Max: <?php echo $product['stock_quantity']; ?> available</small>
                                </div>
                                <div class="col">
                                    <button class="btn btn-primary btn-lg add-to-cart-btn"
                                            data-product-id="<?php echo $product['id']; ?>">
                                        <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mb-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Out of Stock</strong> - This product is currently unavailable.
                        </div>
                    <?php endif; ?>
                    
                    <!-- Debug Console -->
                    <div class="mt-4">
                        <h6>Debug Console:</h6>
                        <div id="debug-console" class="alert alert-secondary" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            <div>Debug console initialized...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Links -->
    <div class="text-center mt-5">
        <h4 class="mb-4">Test Different Versions</h4>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="test-quantity-simple.php" class="btn btn-success">
                <i class="fas fa-test-tube me-2"></i>Simple Test
            </a>
            <a href="product-detail.php?slug=<?php echo $product['slug'] ?? 'test'; ?>" class="btn btn-primary">
                <i class="fas fa-eye me-2"></i>Product Detail
            </a>
            <a href="test-product-buttons.php" class="btn btn-info">
                <i class="fas fa-cogs me-2"></i>Full Test
            </a>
        </div>
    </div>
</div>

<style>
/* Enhanced quantity controls */
.qty-btn {
    border-radius: 0;
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    font-weight: bold;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qty-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    transform: scale(1.05);
}

.qty-btn:active {
    transform: scale(0.95);
}

.qty-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#quantity {
    border: 2px solid #dee2e6;
    border-left: 0;
    border-right: 0;
    font-size: 1.1rem;
    font-weight: bold;
    height: 40px;
}

#quantity:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>

<script>
// Debug function
function debugLog(message) {
    const console = document.getElementById('debug-console');
    const time = new Date().toLocaleTimeString();
    console.innerHTML += '<div>[' + time + '] ' + message + '</div>';
    console.scrollTop = console.scrollHeight;
    console.log('[DEBUG]', message);
}

// Use vanilla JavaScript for better compatibility
document.addEventListener('DOMContentLoaded', function() {
    debugLog('DOM Content Loaded');
    
    const quantityInput = document.getElementById('quantity');
    const increaseBtn = document.getElementById('increase-qty');
    const decreaseBtn = document.getElementById('decrease-qty');
    const addToCartBtn = document.querySelector('.add-to-cart-btn');
    
    debugLog('Elements found:');
    debugLog('- quantityInput: ' + (quantityInput ? 'YES' : 'NO'));
    debugLog('- increaseBtn: ' + (increaseBtn ? 'YES' : 'NO'));
    debugLog('- decreaseBtn: ' + (decreaseBtn ? 'YES' : 'NO'));
    debugLog('- addToCartBtn: ' + (addToCartBtn ? 'YES' : 'NO'));
    
    if (!quantityInput || !increaseBtn || !decreaseBtn) {
        debugLog('ERROR: Required elements not found!');
        return;
    }
    
    function updateQuantityDisplay() {
        const qty = parseInt(quantityInput.value) || 1;
        const max = parseInt(quantityInput.getAttribute('max'));
        
        debugLog('Updating display: qty=' + qty + ', max=' + max);
        
        // Update button states
        decreaseBtn.disabled = qty <= 1;
        increaseBtn.disabled = qty >= max;
        
        // Update add to cart button text
        if (addToCartBtn) {
            addToCartBtn.innerHTML = '<i class="fas fa-cart-plus me-2"></i>Add ' + qty + ' to Cart';
        }
        
        debugLog('Button states: decrease=' + (decreaseBtn.disabled ? 'disabled' : 'enabled') + 
                ', increase=' + (increaseBtn.disabled ? 'disabled' : 'enabled'));
    }
    
    // Increase quantity
    increaseBtn.addEventListener('click', function() {
        debugLog('Increase button clicked');
        const max = parseInt(quantityInput.getAttribute('max'));
        const current = parseInt(quantityInput.value) || 1;
        
        debugLog('Current: ' + current + ', Max: ' + max);
        
        if (current < max) {
            quantityInput.value = current + 1;
            debugLog('Increased to: ' + quantityInput.value);
            
            // Add visual feedback
            this.classList.add('btn-success');
            this.classList.remove('btn-outline-secondary');
            setTimeout(() => {
                this.classList.remove('btn-success');
                this.classList.add('btn-outline-secondary');
            }, 200);
        } else {
            debugLog('Maximum reached!');
            // Show max reached feedback
            this.classList.add('btn-warning');
            this.classList.remove('btn-outline-secondary');
            setTimeout(() => {
                this.classList.remove('btn-warning');
                this.classList.add('btn-outline-secondary');
            }, 500);
            alert('Maximum quantity reached!');
        }
        updateQuantityDisplay();
    });
    
    // Decrease quantity
    decreaseBtn.addEventListener('click', function() {
        debugLog('Decrease button clicked');
        const current = parseInt(quantityInput.value) || 1;
        
        debugLog('Current: ' + current);
        
        if (current > 1) {
            quantityInput.value = current - 1;
            debugLog('Decreased to: ' + quantityInput.value);
            
            // Add visual feedback
            this.classList.add('btn-danger');
            this.classList.remove('btn-outline-secondary');
            setTimeout(() => {
                this.classList.remove('btn-danger');
                this.classList.add('btn-outline-secondary');
            }, 200);
        } else {
            debugLog('Minimum reached!');
            // Show minimum reached feedback
            this.classList.add('btn-warning');
            this.classList.remove('btn-outline-secondary');
            setTimeout(() => {
                this.classList.remove('btn-warning');
                this.classList.add('btn-outline-secondary');
            }, 500);
            alert('Minimum quantity is 1!');
        }
        updateQuantityDisplay();
    });
    
    // Handle manual quantity input
    quantityInput.addEventListener('input', function() {
        debugLog('Input changed to: ' + this.value);
        const max = parseInt(this.getAttribute('max'));
        const min = parseInt(this.getAttribute('min'));
        let current = parseInt(this.value) || 1;
        
        if (current > max) {
            this.value = max;
            debugLog('Capped to max: ' + max);
            alert('Maximum quantity is ' + max);
        } else if (current < min) {
            this.value = min;
            debugLog('Capped to min: ' + min);
            alert('Minimum quantity is ' + min);
        }
        updateQuantityDisplay();
    });
    
    // Add to cart functionality
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            const quantity = parseInt(quantityInput.value) || 1;
            debugLog('Add to cart clicked with quantity: ' + quantity);
            alert('Would add ' + quantity + ' items to cart!');
        });
    }
    
    // Initialize quantity display
    updateQuantityDisplay();
    
    debugLog('Quantity controls initialized successfully');
});
</script>

<?php include 'includes/footer.php'; ?>
