<?php
require_once 'config/config.php';

$page_title = 'Firebase Test';
$page_description = 'Test Firebase Authentication';

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">🔥 Firebase Authentication Test</h2>
                    
                    <div id="auth-status" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>Checking authentication status...
                    </div>
                    
                    <div id="user-info" style="display: none;">
                        <h4>User Information:</h4>
                        <ul id="user-details"></ul>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Test Registration</h5>
                            <form id="testRegisterForm">
                                <div class="mb-2">
                                    <input type="email" class="form-control" id="regEmail" placeholder="Email" value="<EMAIL>">
                                </div>
                                <div class="mb-2">
                                    <input type="password" class="form-control" id="regPassword" placeholder="Password" value="test123">
                                </div>
                                <div class="mb-2">
                                    <input type="text" class="form-control" id="regFirstName" placeholder="First Name" value="Test">
                                </div>
                                <div class="mb-2">
                                    <input type="text" class="form-control" id="regLastName" placeholder="Last Name" value="User">
                                </div>
                                <button type="submit" class="btn btn-success">Test Register</button>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>Test Login</h5>
                            <form id="testLoginForm">
                                <div class="mb-2">
                                    <input type="email" class="form-control" id="loginEmail" placeholder="Email" value="<EMAIL>">
                                </div>
                                <div class="mb-2">
                                    <input type="password" class="form-control" id="loginPassword" placeholder="Password" value="test123">
                                </div>
                                <button type="submit" class="btn btn-primary">Test Login</button>
                            </form>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button id="logoutBtn" class="btn btn-warning" style="display: none;">Test Logout</button>
                        <button id="resetBtn" class="btn btn-info">Test Password Reset</button>
                    </div>
                    
                    <div id="test-results" class="mt-4"></div>
                    
                    <div class="mt-4">
                        <h5>Quick Links:</h5>
                        <a href="login_firebase.php" class="btn btn-outline-primary me-2">Firebase Login Page</a>
                        <a href="register_firebase.php" class="btn btn-outline-success me-2">Firebase Register Page</a>
                        <a href="index.php" class="btn btn-outline-secondary">Home Page</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="module">
import { firebaseAuth, authHelpers } from './js/firebase-config.js';

document.addEventListener('DOMContentLoaded', function() {
    const authStatus = document.getElementById('auth-status');
    const userInfo = document.getElementById('user-info');
    const userDetails = document.getElementById('user-details');
    const logoutBtn = document.getElementById('logoutBtn');
    const testResults = document.getElementById('test-results');
    
    function showResult(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        testResults.innerHTML += `
            <div class="alert ${alertClass} alert-dismissible fade show">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }
    
    // Listen for auth state changes
    firebaseAuth.onAuthStateChanged((user) => {
        if (user) {
            authStatus.innerHTML = '<i class="fas fa-check-circle me-2"></i>User is logged in';
            authStatus.className = 'alert alert-success';
            
            userDetails.innerHTML = `
                <li><strong>UID:</strong> ${user.uid}</li>
                <li><strong>Email:</strong> ${user.email}</li>
                <li><strong>Display Name:</strong> ${user.displayName || 'Not set'}</li>
                <li><strong>Email Verified:</strong> ${user.emailVerified}</li>
                <li><strong>Created:</strong> ${new Date(user.metadata.creationTime).toLocaleString()}</li>
                <li><strong>Last Sign In:</strong> ${new Date(user.metadata.lastSignInTime).toLocaleString()}</li>
            `;
            userInfo.style.display = 'block';
            logoutBtn.style.display = 'inline-block';
        } else {
            authStatus.innerHTML = '<i class="fas fa-times-circle me-2"></i>No user logged in';
            authStatus.className = 'alert alert-warning';
            userInfo.style.display = 'none';
            logoutBtn.style.display = 'none';
        }
    });
    
    // Test registration
    document.getElementById('testRegisterForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = document.getElementById('regEmail').value;
        const password = document.getElementById('regPassword').value;
        const firstName = document.getElementById('regFirstName').value;
        const lastName = document.getElementById('regLastName').value;
        
        showResult('Testing registration...', 'info');
        
        const result = await firebaseAuth.register(email, password, firstName, lastName, '');
        
        if (result.success) {
            showResult(`✅ Registration successful! User: ${result.user.email}`, 'success');
        } else {
            showResult(`❌ Registration failed: ${result.error}`, 'error');
        }
    });
    
    // Test login
    document.getElementById('testLoginForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;
        
        showResult('Testing login...', 'info');
        
        const result = await firebaseAuth.login(email, password);
        
        if (result.success) {
            showResult(`✅ Login successful! User: ${result.user.email}`, 'success');
        } else {
            showResult(`❌ Login failed: ${result.error}`, 'error');
        }
    });
    
    // Test logout
    logoutBtn.addEventListener('click', async () => {
        showResult('Testing logout...', 'info');
        
        const result = await firebaseAuth.logout();
        
        if (result.success) {
            showResult('✅ Logout successful!', 'success');
        } else {
            showResult(`❌ Logout failed: ${result.error}`, 'error');
        }
    });
    
    // Test password reset
    document.getElementById('resetBtn').addEventListener('click', async () => {
        const email = document.getElementById('loginEmail').value;
        
        if (!email) {
            showResult('❌ Please enter an email address first', 'error');
            return;
        }
        
        showResult('Testing password reset...', 'info');
        
        const result = await firebaseAuth.resetPassword(email);
        
        if (result.success) {
            showResult(`✅ Password reset email sent to ${email}`, 'success');
        } else {
            showResult(`❌ Password reset failed: ${result.error}`, 'error');
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
